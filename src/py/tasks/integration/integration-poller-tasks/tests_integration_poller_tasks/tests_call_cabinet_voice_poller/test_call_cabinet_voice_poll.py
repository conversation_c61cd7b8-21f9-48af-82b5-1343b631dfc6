# flake8: noqa: E402
import hashlib
import os
from pathlib import Path

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

import botocore.exceptions
import datetime
import pytest
from addict import addict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll import (
    CallCabinetVoicePoll,
)
from mock import mock
from mock.mock import DEFAULT, patch


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="call_cabinet_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_back_fill() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="call_cabinet_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="call_cabinet_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
            custom_lake_path="boarding/",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


MOCK_RESPONSE = {
    "paused": False,
    "id": 3,
    "max_batch_size": None,
    "workflow_last_executed": None,
    "time_created": "2023-06-09T10:06:14.733020",
    "io_topic": "mock_topic",
    "tenant_id": 1,
    "workflow_id": 3,
    "batch_timeout_s": None,
    "workflow_execution_ref": None,
    "time_updated": None,
    "tenant": {
        "id": 1,
        "lake_prefix": "s3://pinafore.dev.steeleye.co/",
        "time_updated": None,
        "stack_id": 1,
        "name": "pinafore",
        "time_created": "2023-06-09T10:00:22.860947",
        "stack": {
            "paused": False,
            "name": "dev-blue",
            "time_updated": None,
            "id": 1,
            "time_created": "2023-06-09T10:00:03.477200",
        },
    },
    "workflow": {
        "s3_feed_prefix": "test",
        "name": "call_cabinet_voice_poll",
        "time_created": "2023-06-09T10:02:34.313252",
        "id": 3,
        "streamed": False,
        "time_updated": None,
    },
}

MOCK_SECRETS = {"APIKey": "test", "CustomerID": "test", "SiteID": "test"}

EXPECTED_EVENT = IOEvent(
    workflow=WorkflowFieldSet(
        name="call_cabinet_voice_poll",
        stack="local",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    ),
    task=TaskFieldSet(name="call_cabinet_voice_poll", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            file_uri=mock.ANY,
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT.workflow.trace_id = mock.ANY
EXPECTED_EVENT.task.id = mock.ANY
EXPECTED_EVENT.task.timestamp = mock.ANY
EXPECTED_EVENT.workflow.start_timestamp = mock.ANY


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
)
def test_call_cabinet_success_with_custom_path(sample_input_with_custom_path, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = [
        {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
        {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
    ]
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].return_value = "wav"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input_with_custom_path)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
)
def test_call_cabinet_success(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = [
        {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
        {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
    ]
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].return_value = "wav"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
    write=DEFAULT,
)
def test_call_cabinet_success_with_multiple_connection(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(
        {
            "connection_1": MOCK_SECRETS,
            "connection_2": MOCK_SECRETS,
        }
    )
    mock_get_response = [
        [
            {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
            {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
        ],
        [
            {"CallId": "example3", "StartTime": "2023-06-10 10:00:22.860947"},
            {"CallId": "example4", "StartTime": "2023-06-10 10:00:22.860947"},
        ],
    ]
    kwargs["requests"].get.return_value.json.side_effect = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].return_value = "wav"

    # Connection 1 call
    sample_input.input_param.params["connection_identifier"] = "connection_1"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )
    # Assert that we are uploading the correct attachments for 1st connection
    assert (
        str(Path(kwargs["write"].call_args_list[0][1]["target_path"]).stem)
        == hashlib.md5(mock_get_response[0][0]["CallId"].encode()).hexdigest()
    )
    assert (
        str(Path(kwargs["write"].call_args_list[1][1]["target_path"]).stem)
        == hashlib.md5(mock_get_response[0][1]["CallId"].encode()).hexdigest()
    )

    # Connection 2 call
    sample_input.input_param.params["connection_identifier"] = "connection_2"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )
    # Assert that we are uploading the correct attachments for 2nd connection
    assert (
        str(Path(kwargs["write"].call_args_list[2][1]["target_path"]).stem)
        == hashlib.md5(mock_get_response[1][0]["CallId"].encode()).hexdigest()
    )
    assert (
        str(Path(kwargs["write"].call_args_list[3][1]["target_path"]).stem)
        == hashlib.md5(mock_get_response[1][1]["CallId"].encode()).hexdigest()
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
)
def test_call_cabinet_success_backfill(sample_input_back_fill, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = [
        {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
        {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
    ]
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].return_value = "wav"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input_back_fill)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
)
def test_call_cabinet_failure_non_fatal_exception(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = [
        {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
        {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
    ]
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].side_effect = Exception
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input)
    with pytest.raises(Exception):
        poller.run_poller()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.api_voice_poller",
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
)
@patch.multiple(
    "integration_poller_tasks.call_cabinet_voice_poller.call_cabinet_voice_poll",
    requests=DEFAULT,
    guess_filetype=DEFAULT,
    write=DEFAULT,
)
def test_call_cabinet_failure_fatal_exception(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = [
        {"CallId": "example1", "StartTime": "2023-06-09 10:00:22.860947"},
        {"CallId": "example2", "StartTime": "2023-06-09 10:00:22.860947"},
    ]
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["guess_filetype"].return_value = "wav"
    poller = CallCabinetVoicePoll(config=config, aries_task_input=sample_input)
    kwargs["write"].side_effect = botocore.exceptions.ClientError({}, "PutObject")
    with pytest.raises(botocore.exceptions.ClientError):
        poller.run_poller()
