aws_region: ${oc.env:AWS_REGION,null}
data_platform_config_api_url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL,https://aries-platform-config.dev-enterprise.steeleye.co}
graph_api_timeout_s: ${oc.env:GRAPH_API_TIMEOUT_S,900}
kafka_rest_proxy_url: ${oc.env:KAFKA_REST_PROXY_URL,test-url}
stack: ${oc.env:STACK,local}
vault:
  auth_method: ${oc.env:VAULT_AUTH_METHOD}
  k8s_auth_mount_point: ${oc.env:VAULT_K8S_AUTH_MOUNT_POINT}
  k8s_jwt_path: ${oc.env:VAULT_K8S_JWT_PATH}
  k8s_role: ${oc.env:VAULT_K8S_ROLE}
  mount_point: ${oc.env:VAULT_MOUNT_POINT}
  token: ${oc.env:VAULT_TOKEN}
  url: ${oc.env:VAULT_URL}
version: ${oc.env:SE_VERSION,"latest"}
