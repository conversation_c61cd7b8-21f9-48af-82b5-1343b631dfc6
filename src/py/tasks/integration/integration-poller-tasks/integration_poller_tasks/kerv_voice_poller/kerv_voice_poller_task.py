import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.kerv_voice_poller.kerv_voice_poll import Kerv<PERSON><PERSON><PERSON><PERSON>oll
from integration_poller_tasks.kerv_voice_poller.static import POLLER_NAME, TRANSFORM_FLOW_NAME
from omegaconf import OmegaConf
from pathlib import Path

logger = logging.getLogger(POLLER_NAME)


def kerv_voice_poll_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    config = OmegaConf.load(Path(__file__).parent.joinpath("kerv-voice-poller-config.yml"))
    poller = KervVoicePoll(
        aries_task_input=aries_task_input,
        config=config,
        transform_workflow_name=TRANSFORM_FLOW_NAME,
    )
    return poller.run_poller()
