# type: ignore
import logging
from benchmark_mode import benchmark
from integration_audio_comms_tasks.integration_audio_comms_tasks_task import (
    integration_audio_comms_tasks_run,
)
from integration_audio_comms_tasks.voice.verba_voice_transform.verba_voice_transform_sample_input import (  # noqa E510
    sample_input,
)

logger = logging.getLogger("verba_voice_transform")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_audio_comms_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
