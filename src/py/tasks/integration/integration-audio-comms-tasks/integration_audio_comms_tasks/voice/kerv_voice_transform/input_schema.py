from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput
from pydantic import Field
from se_conductor_utils.task_output import DynamicTask
from typing import Dict


class KervVoiceTransformAriesTaskInput(IntegrationAriesTaskInput):
    """KervVoiceTransform requires a DynamicTask input so that it can generate
    a bunch of outputs in a dynamic fork join."""

    dynamic_tasks: Dict[str, DynamicTask] = Field(
        ...,
        description=(
            "Dict containing the definition for call and waveform dynamic tasks."
            "e.g. input: { 'call': `DynamicTask`, 'waveform': `DynamicTask` }"
        ),
    )
