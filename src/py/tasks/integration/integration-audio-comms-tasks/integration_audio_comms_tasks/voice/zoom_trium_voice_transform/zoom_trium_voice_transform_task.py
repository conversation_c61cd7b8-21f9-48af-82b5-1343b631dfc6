import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_audio_comms_tasks.voice.zoom_trium_voice_transform.app_metrics_template import (
    APP_METRICS,
)
from integration_audio_comms_tasks.voice.zoom_trium_voice_transform.zoom_trium_voice_transform_flow import (  # noqa E501
    zoom_trium_voice_transform_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("zoom_trium_voice_transform")


def zoom_trium_voice_transform_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=zoom_trium_voice_transform_flow)
