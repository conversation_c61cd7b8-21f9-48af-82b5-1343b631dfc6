from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_audio_comms_tasks.voice.o2_voice_transform.app_metrics_template import APP_METRICS
from integration_audio_comms_tasks.voice.o2_voice_transform.o2_voice_transform_flow import (
    o2_voice_transform_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask


def o2_voice_transform_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=o2_voice_transform_flow)
