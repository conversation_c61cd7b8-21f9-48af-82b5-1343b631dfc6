import boto3
import fsspec
import json
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns  # type: ignore
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_audio_comms_tasks.voice.commpeak_voice_transform.commpeak_voice_transform_task import (  # noqa E510
    commpeak_voice_transform_run,  # type: ignore
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_wrapper.static import StaticFields
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Call
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from shutil import rmtree
from typing import List
from unittest.mock import patch

BUCKET_NAME: str = "test.dev.steeleye.co"

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

CURRENT_PATH = Path(__file__).parent

LOCAL_BUCKETS_PATH = CURRENT_PATH.joinpath("data/buckets")

DATA_PATH = Path(__file__).parent.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
EXPECTED_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath("expected_output.ndjson")


mock_aiobotocore_convert_to_response_dict()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(TEMP_DIR)

    request.addfinalizer(_end)


class TestCommpeakVoiceTransform:
    """Test suite for Callcabinet Voice Transform."""

    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_it_can_run_end_to_end(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
            bucket_name=BUCKET_NAME,
        )

        ndjson_s3_uri: str = (
            aries_task_result.output_param.params.get("Call").get("params").get("file_uri")
        )

        waveform_s3_uri: str = (
            aries_task_result.output_param.params.get("Waveform").get("params").get("file_uri")
        )

        local_file_path: str = run_download_file(
            file_url=ndjson_s3_uri,
        )

        final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        final_result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

        # Sort identifiers columns
        sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)

        def update_voice_file(row):
            if "voiceFile" not in row:
                return row

            # as the original file is converted to mp3 by the media converter
            # task, the produced file will always have a distinct name
            assert row["voiceFile"]["fileInfo"]["location"]["key"] is not None
            assert str(row["voiceFile"]["fileInfo"]["location"]["key"]).endswith(".mp3") is True

            assert row["voiceFile"]["fileName"] is not None
            assert str(row["voiceFile"]["fileName"]).endswith(".mp3") is True

            row["voiceFile"]["fileInfo"]["location"]["key"] = None
            row["voiceFile"]["fileName"] = None

            return row

        final_result = final_result.apply(update_voice_file, axis=1)
        final_result_expected = final_result_expected.apply(update_voice_file, axis=1)

        pd.testing.assert_frame_equal(
            left=final_result.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            right=final_result_expected.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            check_like=True,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "commpeak_voice"
            ]["commpeak_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 1

        assert aries_task_result.output_param.params == {
            "Call": {
                "params": {
                    "file_uri": ndjson_s3_uri,
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": Call.get_reference().get_qualified_reference(),
                }
            },
            "Waveform": {
                "params": {
                    "file_uri": waveform_s3_uri,
                }
            },
        }

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_files": {},
            "workflow_status": ["Total calls discarded as they were shorter than 60 seconds: 1"],
        }

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_it_can_handle_batch_with_only_recordings(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        """
        - This test ensure that a batch containing only recordings can
        be handled without any issues.
        - In this case the batch contains only recordings and no metadata files
        exist either in the batch or in s3.
        """
        bucket_name: str = "test2.dev.steeleye.co"

        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest_batching/commpeak_voice/2024/03/19/batch_file.ndjson"
        )

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
            bucket_name=bucket_name,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "commpeak_voice"
            ]["commpeak_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0

        assert aries_task_result.output_param.params == {
            "Call": None,
            "Waveform": None,
        }

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_files": {},
            "workflow_status": [
                "No data was ingested because metadata "
                "files were not found for the given recordings."
            ],
        }

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_it_can_handle_batch_with_only_metadata_files_under_60_seconds(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        """
        - This test ensure that a batch containing only metadata files
        with call duration less than 60 seconds can be handled without any issues.
        """
        bucket_name: str = "test3.dev.steeleye.co"

        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest_batching/commpeak_voice/2024/03/19/batch_file.ndjson"
        )

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
            bucket_name=bucket_name,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "commpeak_voice"
            ]["commpeak_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0

        assert aries_task_result.output_param.params == {
            "Call": None,
            "Waveform": None,
        }

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_files": {},
            "workflow_status": ["No calls found with duration greater or equal to 60 seconds."],
        }

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
    )
    @patch("integration_wrapper.integration_aries_task.write_named_temporary_json")
    def _run_aries_task(
        write_named_temporary_json_mock,
        mock_cached_tenant_workflow_api_client,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        link_participants_scroll_result: dict,
        sample_aries_task_input: AriesTaskInput,
        bucket_name: str = BUCKET_NAME,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input

        # replace write_named_temporary_json side effect
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        mock_cached_tenant_workflow_api_client.return_value = addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{bucket_name}",
                },
                "workflow": {"streamed": True},
            }
        )

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return commpeak_voice_transform_run(aries_task_input=aries_task_input)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    bucket_path = LOCAL_BUCKETS_PATH.joinpath(bucket_name)

    for file_ in bucket_path.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{bucket_path}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
