import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="zoom_trium_voice",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri=f"s3://test.dev.steeleye.co/aries/ingest_batching/zoom_trium_voice/2024/05/20/zoom_trium_batch.ndjson",  # noqa E501
        )
    )
    task = TaskFieldSet(name="zoom_trium_voice_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&key": "AccountPerson:8c2ed042-25fe-5161-acf0-3dbf52aeb82d:*************",
            "&timestamp": *************,
            "uniqueIds": ["<EMAIL>"],
            "&uniqueProps": ["<EMAIL>"],
            "&model": "MarketPerson",
            "name": "Person One",
            "communications.phoneNumbers": [
                {"dialingCode": "244", "label": "MOBILE", "number": "+************"}
            ],
            "personalDetails": {"firstName": "Person", "lastName": "One"},
        },
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d7",
            "&key": "AccountPerson:8c2ed042-25fe-5161-acf0-3dbf52aeb82d:*************",
            "&timestamp": *************,
            "uniqueIds": ["<EMAIL>"],
            "&uniqueProps": ["<EMAIL>"],
            "&model": "MarketPerson",
            "name": "Person Two",
            "communications.phoneNumbers": [
                {"dialingCode": "244", "label": "MOBILE", "number": "447702"}
            ],
            "personalDetails": {"firstName": "Person", "lastName": "Two"},
        },
    ]
    return pd.DataFrame(list_dict)
