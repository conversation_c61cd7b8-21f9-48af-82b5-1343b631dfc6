from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_kerv_text_transform1",
        name="kerv_text",
        stack="uat-shared-1",
        tenant="dcme",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://dcme.uat.steeleye.co/aries/ingest_batching/kerv_text/2024/08/02/7Udtq2pMIKdCgl3pEVbUw/dfed00305514a93d178917d4f438ec89e1e8de4a5b13ea5487e928019cdcb21d___batch.ndjson",
            streamed=True,
        )
    )

    task = TaskFieldSet(name="kerv_text_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
