from integration_trades_tasks.trade_sink.handler.engadine.engadine_eze import (
    TradeHandler as EngadineTradeHandler,
)
from integration_trades_tasks.trade_sink.handler.engadine.engadine_eze import eze_file_type
from integration_trades_tasks.trade_sink.handler.foresightgroup import foresightgroup_transform
from integration_trades_tasks.trade_sink.utils.base_classes.trade_handler import (
    AbstractTradeHandler,
)


class TradeHandler(EngadineTradeHandler):
    def __init__(self, content, event, firm, data_source):
        AbstractTradeHandler.__init__(self, content, event, firm, data_source)
        self.file = eze_file_type(self.event, foresightgroup_transform)
        self.header_line_prefix = "TRADE_ID"  # first value in header row
