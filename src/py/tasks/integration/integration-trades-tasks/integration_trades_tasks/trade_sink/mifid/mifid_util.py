# ruff: noqa: E501
# mypy: disable-error-code="call-overload, return, misc, arg-type"
import csv
import os
import re
from addict import addict
from integration_trades_tasks.trade_sink.utils import trade_util as utl
from integration_trades_tasks.trade_sink.utils.convention import nested_read as nr
from integration_trades_tasks.trade_sink.utils.convention import nested_write as nw
from integration_trades_tasks.trade_sink.utils.data_util import double, is_empty
from integration_trades_tasks.trade_sink.utils.decorators import error_location
from integration_trades_tasks.trade_sink.utils.exception import InternalRequiredError
from integration_trades_tasks.trade_sink.utils.static import TempColumns
from pathlib import Path
from se_elastic_schema.static.market import IdentifierType
from se_elastic_schema.validators.iso.lei import LEI
from se_trades_tasks.order.party.static import PARTY_FILE_ID_FALLBACK_COL_MAPPING
from se_trades_tasks.order.static import ModelPrefix, OrderColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, PartyPrefix

DIRECTORY = Path(os.path.dirname(__file__))

with DIRECTORY.joinpath("exchange-code-mic-mapping.csv").open(encoding="utf-8-sig") as f:
    EX_CO_MAPPINGS = [row for row in csv.DictReader(f)]

with DIRECTORY.joinpath("systematicInternaliserLeiToMicList.csv").open() as f:
    LEI_TO_MIC = {row["lei"]: row["mic"] for row in csv.DictReader(f)}

with DIRECTORY.joinpath("systematicInternaliserMicList.csv").open() as f:
    MIC_SET = {row["mic"] for row in csv.DictReader(f)}


PRICE_FIELDS = dict(
    Order=[
        ("LastPx", "priceFormingData.price"),
        (
            "Instrument.StrikePrice",
            "instrumentDetails.instrument.derivative.strikePrice",
        ),
        ("Price2", "executionDetails.limitPrice"),
        ("StopPx", "executionDetails.stopPrice"),
    ],
    Transaction=[
        ("LastPx", "transactionDetails.price"),
        (
            "Instrument.StrikePrice",
            "instrumentDetails.instrument.derivative.strikePrice",
        ),
    ],
)


class CfiAttribute4:
    AUCTION = "Auction"
    CASH = "Cash"
    PHYSICAL = "Physical"


@error_location
def date(fix, mifid):
    if fix.Flags.SkipRequired:
        return

    dtms = fix.TrdRegTimestamps
    dt = dtms[0]["TrdRegTimestamp"]
    if dt:
        mifid.date = dt[0:10]
    else:
        raise InternalRequiredError(f"TrdRegTimestamps.TrdRegTimestamp: {dt}")


@error_location
def buy_sell(fix, mifid):
    side_groups = fix.TrdCapRptSideGrp
    mifid.buySell = side_groups[0]["Side"]

    # Needed for assign meta parent
    mifid[ModelPrefix.ORDER].buySell = mifid.buySell


@error_location
def normalize(fix):
    if isinstance(fix.Instrument.SecurityIDSource, str):
        fix.Instrument.SecurityIDSource = [fix.Instrument.SecurityIDSource]

    for k, v in dict(TradeID=fix.TradeID, OrderID=fix.OrderID).items():
        fix[k] = v.upper() if isinstance(v, str) else v


@error_location
def instrument_identifiers(fix, record):
    """Generate Instrument Identifiers in the same way that Link Instruments
    task expects it."""
    security_id, security_alt_id = get_security_ids(fix)

    identifiers_list = list()

    iteration_list = list()
    for id_ in [security_id, security_alt_id]:
        iteration_list.extend(id_) if isinstance(id_, list) else iteration_list.append(id_)

    for ins_id in iteration_list:
        if ins_id:
            identifiers_list.append(
                {"labelId": ins_id, "path": INSTRUMENT_PATH, "type": IdentifierType.OBJECT}
            )
    record[OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT] = identifiers_list

    # Create necessary temp fields for instrument linkup
    record[TempColumns.INSTRUMENT_VENUE] = fix.Instrument.SecurityExchange or None
    record[TempColumns.ASSET_CLASS] = fix.AssetClass or None
    record[TempColumns.INSTRUMENT_CREATED_THROUGH_FALLBACK] = True


@error_location
def party_identifiers(fix, record):
    """Generate Party Identifiers in the same way that Link Parties task
    expects it."""
    identifier_type_mapping = {
        "Client": PartiesFields.PARTIES_CLIENT,
        "Buyer": PartiesFields.PARTIES_BUYER,
        "Seller": PartiesFields.PARTIES_SELLER,
        "ExecutingFirm": PartiesFields.PARTIES_EXECUTING_ENTITY,
        "Counterparty": PartiesFields.PARTIES_COUNTERPARTY,
        "Trader": PartiesFields.PARTIES_TRADER,
        "DecisionMakerWithinFirm": PartiesFields.PARTIES_INVEST_DEC_WITHIN_FIRM,
        "ExecutionWithinFirm": PartiesFields.PARTIES_EXECUTION_WITHIN_FIRM,
    }
    record_party_identifiers = list()
    for identifier_type, party_identifier_data in fix.SteelEyeParties.items():
        resolved_object_type = (
            IdentifierType.ARRAY
            if identifier_type in ["Buyer", "Seller", "Trader", "Client"]
            else IdentifierType.OBJECT
        )
        if not isinstance(party_identifier_data, list):
            party_identifier_data = [party_identifier_data]

        for data in party_identifier_data:
            if not data.get("PartyID"):
                continue
            label_id = f"{PartyPrefix.LEI if LEI.validate_lei_code(data.get('PartyID')).is_valid else PartyPrefix.ID}{data.get('PartyID')}"
            role = data.get("PartyRole")
            identifier_path = identifier_type_mapping.get(identifier_type)

            if identifier_path == PartiesFields.PARTIES_BUYER and role == "122":
                identifier_path = PartiesFields.PARTIES_BUYER_DECISION_MAKER
            elif identifier_path == PartiesFields.PARTIES_SELLER and role == "122":
                identifier_path = PartiesFields.PARTIES_SELLER_DECISION_MAKER

            record_party_identifiers.append(
                {"labelId": label_id.lower(), "path": identifier_path, "type": resolved_object_type}
            )

    record[OrderColumns.MARKET_IDENTIFIERS_PARTIES] = record_party_identifiers
    party_file_identifiers(fix, record)

    # Strip off any prefixes (id:, lei:, account:) and return fallback fields
    for file_id_col, fallback_col in PARTY_FILE_ID_FALLBACK_COL_MAPPING.items():
        if not record[file_id_col]:
            continue

        record[fallback_col] = re.sub(
            rf"^({PartyPrefix.ID}|{PartyPrefix.LEI}|{PartyPrefix.ACCOUNT})", "", record[file_id_col]
        )


def get_security_ids(fix):
    security_id = fix.Instrument.SecurityID or ""
    security_id = (
        [id_ for id_ in security_id if id_ is not None]
        if isinstance(security_id, list)
        else security_id
    )
    security_alt_id = ""
    for group in fix.Instrument.SecurityAltGrp or list():
        if group.SecurityAltIDSource == "U":
            security_alt_id = group.SecurityAltID or ""
            break

    return security_id, security_alt_id


@error_location
def long_or_short(fix, record, model="Order"):
    """LARS."""
    groups = fix.TrdCapRptSideGrp or list()
    indicator = None
    if len(groups) > 0 and groups[0].Side:
        indicator = "BUYI" if groups[0].Side in ("1", "3", "9", "A", "D", "F") else "SELL"
    if model == "Order":
        record.executionDetails.buySellIndicator = indicator
    record.transactionDetails.buySellIndicator = indicator


@error_location
def trading_capacity(fix, record, model="Order"):
    """TED."""
    tc_groups = fix.TrdCapRptSideGrp or list()
    if len(tc_groups) > 0:
        oc = tc_groups[0].TradeReportOrderDetail.OrderCapacity or None
        if oc in ("MTCH", "AOTC", "DEAL"):
            record.transactionDetails.tradingCapacity = oc
        elif oc == "A":
            record.transactionDetails.tradingCapacity = "MTCH"
        elif oc in ("M", "W"):
            record.transactionDetails.tradingCapacity = "AOTC"
        elif oc in ("G", "I", "P", "R"):
            record.transactionDetails.tradingCapacity = "DEAL"

        if model == "Order":
            record.executionDetails.tradingCapacity = record.transactionDetails.tradingCapacity


@error_location
def pricing(fix, record):
    """PRINCE."""
    payment_groups = fix.PaymentGrp or list()
    if len(payment_groups) > 0:
        payment_type = payment_groups[0].PaymentType or None
        payment_pay_side = payment_groups[0].PaymentPaySide or None
        payment_amount = payment_groups[0].PaymentAmount or 0.0
        if payment_type == "1" and payment_pay_side in ("2", "5", "6"):
            record.transactionDetails.upFrontPayment = payment_amount
        elif payment_type == "1" and payment_pay_side in ("1", "3", "9", "A", "H"):
            record.transactionDetails.upFrontPayment = float(payment_amount) * -1

        record.transactionDetails.upFrontPaymentCurrency = payment_groups[0].PaymentCurrency or None

    if fix.PriceType in ("PERC", "MONE", "BAPO", "YIEL"):
        record.transactionDetails.priceNotation = fix.PriceType
    elif fix.PriceType == "1":
        record.transactionDetails.priceNotation = "PERC"
    elif fix.PriceType == "3":
        record.transactionDetails.priceNotation = "MONE"
    elif fix.PriceType == "6":
        record.transactionDetails.priceNotation = "BAPO"
    elif fix.PriceType == "9":
        record.transactionDetails.priceNotation = "YIEL"


@error_location
def quantity_notation(fix, record):
    """QUENTIN."""
    if fix.QtyType in ("UNIT", "NOML", "MONE"):
        record.transactionDetails.quantityNotation = fix.QtyType
    elif fix.QtyType == "NOMI":
        record.transactionDetails.quantityNotation = "NOML"
    elif fix.QtyType in ("0", "1", "2"):
        record.transactionDetails.quantityNotation = "UNIT"
    elif fix.QtyType == "95":
        record.transactionDetails.quantityNotation = "NOML"
    elif fix.QtyType == "96":
        record.transactionDetails.quantityNotation = "MONE"


@error_location
def securities_financing_transaction_indicator(fix, record):
    """SECONDUS."""
    if fix.SecuritiesFinancingTxnIndicator:
        record.tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator = (
            fix.SecuritiesFinancingTxnIndicator
        )
    else:
        record.tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator = (
            len(
                list(
                    filter(
                        lambda trp: trp.get("TrdRegPublicationType") == "2"
                        and trp.get("TrdRegPublicationReason") == "11",
                        fix.TrdRegPublicationGrp or list(),
                    )
                )
            )
            > 0
        )


@error_location
def waiver_indicator(fix, record):
    """WALDO."""
    indicators = set()

    for group in fix.TrdRegPublicationGrp or list():
        pub_type = group.TrdRegPublicationType
        pub_reason = group.TrdRegPublicationReason
        if pub_type == "0" and pub_reason == "0":
            indicators.add("NLIQ")
        elif pub_type == "0" and pub_reason == "1":
            indicators.add("OLIQ")
        elif pub_type == "0" and pub_reason == "2":
            indicators.add("PRIC")
        elif pub_type == "0" and pub_reason == "3":
            indicators.add("RFPT")
        elif pub_type == "0" and pub_reason == "4":
            indicators.add("ILQD")
        elif pub_type == "0" and pub_reason == "5":
            indicators.add("SIZE")

    record.tradersAlgosWaiversIndicators.waiverIndicator = sorted(list(indicators))


def party_file_identifiers(fix, record):
    record.clientFileIdentifier = single_party_id(fix, "Client")
    record.traderFileIdentifier = single_party_id(fix, "Trader")
    record.counterpartyFileIdentifier = single_party_id(fix, "Counterparty")
    record.buyerFileIdentifier = next(multi_party_ids(fix, "Buyer", "27"), None)
    record.buyerDecisionMakerFileIdentifier = next(multi_party_ids(fix, "Buyer", "122"), None)
    record.sellerFileIdentifier = next(multi_party_ids(fix, "Seller", "27"), None)
    record.sellerDecisionMakerFileIdentifier = next(multi_party_ids(fix, "Seller", "122"), None)
    record.executingEntityFileIdentifier = single_party_id(fix, "ExecutingFirm")
    record.reportDetails.executingEntity.fileIdentifier = record.executingEntityFileIdentifier
    algo_ind = record.tradersAlgosWaiversIndicators
    algo_ind.executionWithinFirmFileIdentifier = single_party_id(fix, "ExecutionWithinFirm")

    if acting_for_single_client(fix):
        algo_ind.investmentDecisionWithinFirmFileIdentifier = single_party_id(
            fix, "DecisionMakerWithinFirm"
        )


# “Investment decision maker not able to be attributed on a Market Side
# transaction in the case of grouped orders (i.e., and use of the
# aggregated client account) due to the possibility of multiple decision makers
# for multiple clients - ON-663”
def acting_for_single_client(fix):
    if fix.RecordType != "Market Side":
        return True

    buyer_id = next(multi_party_ids(fix, "Buyer", "27"), "").split(":")[-1]
    seller_id = next(multi_party_ids(fix, "Seller", "27"), "").split(":")[-1]
    return not any(identifier == "intc" for identifier in [buyer_id, seller_id])


def single_party_id(fix, field) -> str:
    entity = fix.SteelEyeParties.get(field)
    if isinstance(entity, dict) and entity.get("PartyID"):
        p_src = entity.get("PartyIDSource") or ""
        p_id = entity.get("PartyID") or ""
        return f"{p_src}:{p_id}".lower()


def multi_party_ids(fix, field, role) -> str:
    for entity in fix.SteelEyeParties.get(field, list()):
        if isinstance(entity, dict) and entity.get("PartyRole") == role and entity.get("PartyID"):
            p_src = entity.get("PartyIDSource") or ""
            p_id = entity.get("PartyID") or ""
            yield f"{p_src}:{p_id}".lower()


@error_location
def position_effect(fix, record):
    if "PositionEffect" in fix:
        pe = fix.PositionEffect
        pe_map = {
            "C": "Close",
            "F": "FIFO",
            "O": "Open",
            "R": "Rolled",
            "N": "Closed but notify or open",
            "D": "Default",
        }
        record.transactionDetails.positionEffect = pe_map.get(pe) or pe


def numeric_types(record):
    fields = [
        "transactionDetails.netAmount",
        "executionDetails.peggedLimitPrice",
        "transactionDetails.pricingDetails.slippagePrice",
        "transactionDetails.pricingDetails.arrivalPrice",
        "priceFormingData.modifiedQuantity",
        "transactionDetails.pricingDetails.ohlc",
        "transactionDetails.quantity",
        "executionDetails.additionalLimitPrice",
        "transactionDetails.pricingDetails.marketPrice",
        "transactionDetails.priceAverage",
        "priceFormingData.initialQuantity",
        "transactionDetails.pricingDetails.highPrice",
        "priceFormingData.upFrontPayment",
        "executionDetails.limitPrice",
        "transactionDetails.cumulativeQuantity",
        "priceFormingData.tradedQuantity",
        "transactionDetails.pricingDetails.bidAskSpread",
        "transactionDetails.pricingDetails.bidPrice",
        "executionDetails.stopPrice",
        "priceFormingData.ask.native",
        "transactionDetails.pricingDetails.yield",
        "transactionDetails.pricingDetails.lowPrice",
        "transactionDetails.price",
        "transactionDetails.settlementAmount",
        "transactionDetails.upFrontPayment",
        "executionDetails.minAcceptableQuantity",
        "transactionDetails.commissionAmount",
        "transactionDetails.pricingDetails.askPrice",
        "executionDetails.minExecutableSize",
        "executionDetails.settlementAmount",
        "priceFormingData.displayedQuantity",
        "priceFormingData.price",
        "priceFormingData.bid.native",
        "priceFormingData.remainingQuantity",
    ]
    for field in fields:
        path = field.split(".")
        original_value = nr(record, *path)
        if not (isinstance(original_value, addict.Dict) or original_value is None):
            nw(record, double(original_value), *path)


@error_location
def instrument_override_columns(fix, record):
    record[TempColumns.CONTRACT_MULTIPLIER] = fix.Instrument.ContractMultiplier
    record[TempColumns.STRIKE_PRICE] = fix.Instrument.StrikePrice
    record[TempColumns.STRIKE_PRICE_CURRENCY] = fix.Instrument.StrikePriceCurrency
    record[TempColumns.EXPIRY_DATE] = fix.Instrument.ExpiryDate
    record[TempColumns.INSTRUMENT_CLASSIFICATION] = fix.Instrument.InstrumentClassification

    record[TempColumns.DELIVERY_TYPE] = fix.Instrument.InstrumentClassification
    record[TempColumns.INSTRUMENT_CLASSIFICATION] = fix.Instrument.InstrumentClassification

    if not is_empty(fix.Instrument.DeliveryType):
        record[TempColumns.DELIVERY_TYPE] = fix.Instrument.DeliveryType
    else:
        record[TempColumns.DELIVERY_TYPE] = dell(fix.Instrument.InstrumentClassification)

    record[TempColumns.INSTRUMENT_FULL_NAME] = fix.Instrument.instrumentFullName


def dell(inst_class: str):
    if is_empty(inst_class):
        return
    exclude_group = inst_class
    if any("C" in attr for attr in exclude_group):
        return "CASH"
    elif any("P" in attr for attr in exclude_group):
        return "PHYS"


def branch_membership_country(fix: addict.Dict, record: addict.Dict):
    last_market = fix.LastMkt

    if not is_empty(last_market) and len(last_market) == 4 and last_market not in ("XOFF", "XXXX"):
        record.transactionDetails.branchMembershipCountry = (
            record.reportDetails.executingEntity.firmIdentifiers.branchCountry
        )


@error_location
def flags(fix, record):
    if fix.Flags:
        for k, v in fix.Flags.items():
            record["flags"][k] = v


@error_location
def commission_amount(fix, record):
    if is_empty(fix.CommissionAmount):
        return

    commission_amount_type = fix.CommissionAmountType or None
    commission_amount_type = {
        "1": "Amount per unit",
        "2": "Percent",
        "3": "Absolute",
        "4": "Percentage waived - Cash discount basis",
        "5": "Percentage waived - Enhanced units basis",
        "6": "Points per bond or contact",
        "7": "Basis points",
        "8": "Amount per contract",
    }.get(commission_amount_type, commission_amount_type)

    record.transactionDetails.commissionAmount = fix.CommissionAmount
    record.transactionDetails.commissionAmountCurrency = fix.CommissionAmountCurrency
    record.transactionDetails.commissionAmountType = commission_amount_type


@error_location
def complex_trade_component_id(fix, record):
    if not fix.TradeLinkID:
        return
    trade_link_id = "".join((x for x in fix.TradeLinkID if x.isalpha())).upper()
    record.transactionDetails.complexTradeComponentId = trade_link_id


@error_location
def venue_overrides_for_systematic_internalisers(fix, record, systematic_internalisers):
    if systematic_internalisers:
        security_exchange = fix.Instrument.SecurityExchange or None
        if security_exchange in MIC_SET:
            record.transactionDetails.venue = security_exchange

        lei = record.counterparty.firmIdentifiers.lei or None
        mic = LEI_TO_MIC.get(lei)
        if not is_empty(mic):
            record.transactionDetails.venue = mic
            record.transactionDetails.ultimateVenue = mic


@error_location
def to_major_currency(fix: addict.Dict, record: addict.Dict, model: str, mifid_currency_conversion):
    if mifid_currency_conversion:
        currency = fix.Currency
        for fix_field, mifid_field in PRICE_FIELDS[model]:
            price = nr(fix, *fix_field.split("."))
            if is_empty(price):
                continue

            updated_price = utl.price_to_major_currency(price, currency)
            nw(record, updated_price, *mifid_field.split("."))

        record.transactionDetails.priceCurrency = utl.to_major_currency(currency)
