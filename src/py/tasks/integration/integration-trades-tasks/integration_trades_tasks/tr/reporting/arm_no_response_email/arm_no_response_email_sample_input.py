from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_tr_arm_no_response_email_1",
        name="abc",
        stack="dev-blue",
        tenant="puneeth",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(params=dict())

    task = TaskFieldSet(name="tr_arm_no_response_email", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
