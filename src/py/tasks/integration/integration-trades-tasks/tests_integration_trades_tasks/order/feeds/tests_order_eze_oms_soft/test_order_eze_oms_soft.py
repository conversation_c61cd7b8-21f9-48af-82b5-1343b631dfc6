import fsspec
import json
import os
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from integration_test_utils.model_validation.validation_errors import (
    assert_record_is_schema_compliant,
)
from integration_trades_tasks.order.feeds.order_tradeweb import (
    order_tradeweb_flow as order_tradeweb_flow_module,
)
from integration_trades_tasks.order.feeds.order_tradeweb.order_tradeweb_task import (
    order_tradeweb_run,
)
from integration_wrapper.static import St<PERSON><PERSON>ields
from mock import MagicMock
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Order
from se_elastic_schema.steeleye_record_validations.error_codes import SteeleyeRecordErrorCodesEnum
from se_elasticsearch.repository import ResourceConfig
from se_io_utils.json_utils import write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order.best_execution.plugins.best_ex_fx_rates import BestExFxRatesPlugin

CURRENT_PATH = Path(__file__).parent
ORDERS_PATH = CURRENT_PATH.joinpath("data")
TEST_BUCKET = "test.dev.steeleye.co"
TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestOrderEzeOmsSoft:
    @mock_aws
    def test_order_eze_oms_soft_run(self, mocker, sample_input: AriesTaskInput):
        create_and_add_objects_to_s3_bucket(
            bucket_name=TEST_BUCKET,
            source_path=ORDERS_PATH.joinpath("buckets", TEST_BUCKET),
        )

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_input,
        )

        # Assert the NDJSONs produced by the Flow contain the expected data
        for output_key in ["orders", "synthetic_newo"]:
            output_uri: str = (
                aries_task_result.output_param.params.get(output_key).get("params").get("file_uri")  # type: ignore[union-attr]
            )
            local_file_path: str = run_download_file(
                file_url=output_uri,
            )
            result: pd.DataFrame = pd.read_json(local_file_path, lines=True)

            # Assert the output records are schema compliant
            # and do not raise any SteelEye Data validations, apart from the ones
            # that are expected due to having mocked BestExecution
            assert_record_is_schema_compliant(
                input_df=result,
                model=Order,
                accepted_validation_error_codes=[
                    SteeleyeRecordErrorCodesEnum.SE_DV_134,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_136,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_138,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_369,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_371,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_373,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_375,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_142,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_361,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_363,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_365,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_367,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_48,  # bestEx results are mocked
                    SteeleyeRecordErrorCodesEnum.SE_DV_13,
                ],
            )

            expected_result = pd.read_json(
                ORDERS_PATH.joinpath(f"expected_{output_key}.ndjson").as_posix(),
                lines=True,
            )
            os.remove(local_file_path)
            pd.testing.assert_frame_equal(left=result, right=expected_result)

        assert aries_task_result.output_param.params == {  # type: ignore[union-attr]
            "synthetic_newo": {
                "params": {
                    "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_tradeweb/2025/08/25/trace_id/order_tradeweb/a1fcfadae7747143d5ba1cde0db0d9b9ab3df92f11fdc0e32e7903b9c99f6579___order___transformed_synthetic.ndjson",
                    "es_action": "create",
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                }
            },
            "orders": {
                "params": {
                    "file_uri": "s3://test.dev.steeleye.co/aries/ingest/order_tradeweb/2025/08/25/trace_id/order_tradeweb/a1fcfadae7747143d5ba1cde0db0d9b9ab3df92f11fdc0e32e7903b9c99f6579___order___transformed.ndjson",
                    "es_action": "index",
                    "data_model": "se_elastic_schema.models.tenant.mifid2.order:Order",
                }
            },
        }

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "order_tradeweb"
            ]["order_tradeweb"]
            if aries_task_result.app_metric
            else {}
        )

        # Assert app metrics
        # 1 duplicate NEWO in file in first row
        # 1 synthetic NEWO in last row
        # meaning output stays sams as input
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 8
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 1
        assert metrics[OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT] == 1
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 8
        assert metrics[OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT] == 8

    @staticmethod
    @freeze_time(
        time_to_freeze="2025-07-30 08:00:00"
    )  # Must be aligned with date in AriesTaskInput
    def _run_aries_task(mocker, sample_aries_task_input: AriesTaskInput) -> AriesTaskResult:
        aries_task_input = sample_aries_task_input

        mock_search_instruments_cached = mocker.patch(
            "se_trades_tasks.order_and_tr.instrument.link.link_instrument._search_instruments_cached"
        )
        mock_search_instruments_cached.return_value = []

        mock_get_es_config = mocker.patch.object(order_tradeweb_flow_module, "get_es_config")
        mock_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9999,
            scheme="http",
        )

        mock_es_repo = mocker.patch.object(
            order_tradeweb_flow_module, "get_repository_by_cluster_version"
        )

        class MockEsObject:
            def __init__(self):
                self.MAX_TERMS_SIZE = 1024

            @staticmethod
            def scroll(*args, **kwargs):
                """Mock ElasticSearch connections.

                Tasks such as LinkInstruments, LinkParties and
                PartyFallbackWithLeiLookup are already tested
                extensively in se-core/trades-tasks. Other ElasticSearch
                lookup logic, such as the tradedQuantity mapping, is
                tested in the mapping class's tests
                """
                return pd.DataFrame()

            @staticmethod
            def search(*args, **kwargs):
                return addict.Dict(
                    {
                        "hits": {
                            "hits": [
                                {"_source": {"firmIdentifiers": {"lei": "549300BACZP326UOBH06"}}}
                            ]
                        }
                    }
                )

        mock_es_repo.return_value = MockEsObject()

        mock_task = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        mock_task.side_effect = write_named_temporary_json_side_effect
        mocker.patch("integration_audit.auditor.write_json")

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                    "max_batch_size": 10000,
                },
            ),
        )

        # Mock EOD stats fetch for Best Ex
        mock_eod_stats_run = mocker.patch(
            "se_trades_tasks.order.best_execution.best_execution.run_order_eod_stats_enricher"
        )
        mock_eod_stats_run.return_value = pd.DataFrame()

        # Mock ECB Fetch Rates
        mock_ecb_rates = mocker.patch.object(BestExFxRatesPlugin, "get_ecb_rates")
        mock_ecb_rates.return_value = addict.Dict({})

        # Mock party fallback task
        response_mock = MagicMock()
        response_mock.raw_response.status_code = 200
        response_mock.content = dict()

        lei_mock_object = MagicMock()
        lei_mock_object.bulk_fetch_leis.return_value = response_mock

        mock_party_fallback = mocker.patch(
            "se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup.Lei"
        )
        mock_party_fallback.return_value = lei_mock_object

        # Run flow
        result: AriesTaskResult = order_tradeweb_run(aries_task_input=aries_task_input)
        return result


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
