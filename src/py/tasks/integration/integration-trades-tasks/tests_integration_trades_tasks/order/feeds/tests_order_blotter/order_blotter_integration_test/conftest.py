import datetime
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"


@pytest.fixture()
def sample_aries_task_input_aws() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_order_blotter",
        name="order_blotter",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aws_order_blotter_file.csv", skiprows=1, nrows=18
        )
    )
    task = TaskFieldSet(name="order_blotter", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def thornbridge_dma_aries_task_input_aws() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_order_blotter",
        name="order_blotter",
        stack="dev-shared-2",
        tenant="thornbridge",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://thornbridge.dev.steeleye.co/aries/ingress/nonstreamed/evented/DMA_Thornbridge-MIFID-Transactions_Updated_2023-11-10.csv",
            skiprows=1,
            nrows=77,
        )
    )
    task = TaskFieldSet(name="order_blotter", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_ubs() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_order_blotter",
        name="order_blotter",
        stack="dev-golf",
        tenant="golf",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(file_uri="az://golf/ubs_order_blotter_file.csv", skiprows=1, nrows=14)
    )
    task = TaskFieldSet(name="order_blotter", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
