import datetime
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace_id",
        name="order_tradeweb",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime.datetime.strptime("2025-08-25T08:00:00", "%Y-%m-%dT%H:%M:%S"),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_tradeweb/Sample_Blotter_Download.csv",
            skiprows=1,
            nrows=8,
        )
    )
    task = TaskFieldSet(name="order_tradeweb", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
