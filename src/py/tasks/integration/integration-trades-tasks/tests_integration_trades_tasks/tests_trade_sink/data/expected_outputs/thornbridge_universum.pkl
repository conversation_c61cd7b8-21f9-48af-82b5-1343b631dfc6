���n      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�X                                                                         ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKK���C�t�R�h(�               -       /       :       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(��      ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@             �o@             �e@             �z@             �@              l@             �}@             �m@             Px@              ~@             ��@             �_@                                                                                                                                                                                                                                                                                                                                                                             �o@             �e@             �z@             �@              l@             �}@             �m@             Px@              ~@             ��@             �_@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@        ���Q8:@                                                                                                                                                                                                                                                                                                                                                                              �              �              �              �              �              �              �              �              �              �              �        �h�f8�����R�(Kh NNNJ����J����K t�bK
K��ht�R�h(�P                            "       #       $       %       &       '       ;       �hK
��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK.K��h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�}�(�labelId��AU000000NCM7��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hFhGhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhUhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhZhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFh_hHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhdhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhihHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhnhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhshHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFhxhHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFh}hHhIhJhPua]�}�(hF�AU000000NCM7�hHhIhJhPua]�}�(hFh�hHhIhJhPua�ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��ASX - ALL MARKETS�h��1�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
5111454948��
5111454948��
5111455509��
5111455509��
5111454945��
5111454945��
5111455508��
5111455508��
5111454946��
5111454946��
5111455506��
5111455506��
5111455510��
5111455510��
5111454947��
5111454947��
5111455507��
5111455507��
5111455511��
5111455511��
5111468672��
5111468672��2�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21��
2023-06-21�]�(}�(hF�lei:549300tl5406ic1xkd09�hH�buyer�hJhM�ARRAY���R�u}�(hF�id:59700/11693usd1�hH�seller�hJh�u}�(hF�lei:37890036e5cc493eaa85�hH�sellerDecisionMaker�hJh�u}�(hF�lei:37890036e5cc493eaa85�hH�reportDetails.executingEntity�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hH�counterparty�hJhPu}�(hF�id:0�hH�trader�hJh�u}�(hF�id:0�hH�:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�hJhPu}�(hF�id:0�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/11693usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/20392usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/20392usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/30471usd�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/30471usd�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/50805usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/50805usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/51242usd2�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/51242usd2�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/51635usd4�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/51635usd4�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d13290usd4�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d13290usd4�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d15755gbp3�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d15755gbp3�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d15812gbp3�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:59700/d15812gbp3�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:68400/10047usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:68400/10047usd1�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�id:0�hHh�hJh�u}�(hF�id:0�hHh�hJhPu}�(hF�id:0�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:68400/20235usd�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�
id:8479664�hHh�hJh�u}�(hF�
id:8479664�hHh�hJhPu}�(hF�
id:8479664�hHh�hJhPue]�(}�(hF�lei:549300tl5406ic1xkd09�hHh�hJh�u}�(hF�id:68400/20235usd�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJh�u}�(hF�lei:37890036e5cc493eaa85�hHh�hJhPu}�(hF�lei:549300tl5406ic1xkd09�hHh�hJhPu}�(hF�
id:8479664�hHh�hJh�u}�(hF�
id:8479664�hHh�hJhPu}�(hF�
id:8479664�hHh�hJhPue�id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��
id:8479664��
id:8479664��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:59700/11693usd1��id:59700/11693usd1��id:59700/20392usd1��id:59700/20392usd1��id:59700/30471usd��id:59700/30471usd��id:59700/50805usd1��id:59700/50805usd1��id:59700/51242usd2��id:59700/51242usd2��id:59700/51635usd4��id:59700/51635usd4��id:59700/d13290usd4��id:59700/d13290usd4��id:59700/d15755gbp3��id:59700/d15755gbp3��id:59700/d15812gbp3��id:59700/d15812gbp3��id:68400/10047usd1��id:68400/10047usd1��id:68400/20235usd��id:68400/20235usd��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��lei:37890036e5cc493eaa85��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��59700/11693usd1��59700/11693usd1��59700/20392usd1��59700/20392usd1��59700/30471usd��59700/30471usd��59700/50805usd1��59700/50805usd1��59700/51242usd2��59700/51242usd2��59700/51635usd4��59700/51635usd4��59700/d13290usd4��59700/d13290usd4��59700/d15755gbp3��59700/d15755gbp3��59700/d15812gbp3��59700/d15812gbp3��68400/10047usd1��68400/10047usd1��68400/20235usd��68400/20235usd��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��549300tl5406ic1xkd09��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��37890036e5cc493eaa85��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��0��8479664��8479664��Order�j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  ��s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv���s3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/thornbridge_universum/Thornbridge-MIFID-Transactions_New_2023-06-21.csv��0��0��1��1��2��2��3��3��4��4��5��5��6��6��7��7��8��8��9��9��10��10��Thornbridge Universum�j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  j^  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �XASX�G�      �AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��SELL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:51.000000Z��2023-06-21T22:07:50.000000Z��2023-06-21T22:07:50.000000Z��AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �6494565328.DEALLEG1�G�      �6494565488.DEALLEG1�G�      �6494565326.DEALLEG1�G�      �6494565490.DEALLEG1�G�      �6494565330.DEALLEG1�G�      �6494565494.DEALLEG1�G�      �6494565492.DEALLEG1�G�      �6494565334.DEALLEG1�G�      �6494565496.DEALLEG1�G�      �6494565498.DEALLEG1�G�      �6494690908.DEALLEG1�G�      h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��.Symbol - NCM:xasx, Client ID - 59700/11693USD1��.Symbol - NCM:xasx, Client ID - 59700/11693USD1��.Symbol - NCM:xasx, Client ID - 59700/20392USD1��.Symbol - NCM:xasx, Client ID - 59700/20392USD1��-Symbol - NCM:xasx, Client ID - 59700/30471USD��-Symbol - NCM:xasx, Client ID - 59700/30471USD��.Symbol - NCM:xasx, Client ID - 59700/50805USD1��.Symbol - NCM:xasx, Client ID - 59700/50805USD1��.Symbol - NCM:xasx, Client ID - 59700/51242USD2��.Symbol - NCM:xasx, Client ID - 59700/51242USD2��.Symbol - NCM:xasx, Client ID - 59700/51635USD4��.Symbol - NCM:xasx, Client ID - 59700/51635USD4��/Symbol - NCM:xasx, Client ID - 59700/D13290USD4��/Symbol - NCM:xasx, Client ID - 59700/D13290USD4��/Symbol - NCM:xasx, Client ID - 59700/D15755GBP3��/Symbol - NCM:xasx, Client ID - 59700/D15755GBP3��/Symbol - NCM:xasx, Client ID - 59700/D15812GBP3��/Symbol - NCM:xasx, Client ID - 59700/D15812GBP3��.Symbol - NCM:xasx, Client ID - 68400/10047USD1��.Symbol - NCM:xasx, Client ID - 68400/10047USD1��-Symbol - NCM:xasx, Client ID - 68400/20235USD��-Symbol - NCM:xasx, Client ID - 68400/20235USD��Market�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �AGRE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL��NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��
id:8479664��
id:8479664��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��id:0��
id:8479664��
id:8479664�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh(�p                                                               	       
                     
                                                                                                                        !       (       )       *       +       ,       .       0       1       2       3       4       5       6       7       8       9       �hK.��ht�R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(�pandas._libs.missing��NA���j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�b�builtins��slice���K<K=K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  K=K>K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  K>K?K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  K?K@K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  K@KAK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  KAKBK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  KBKCK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  KCKDK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  KDKEK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(]�(hEh�h�h�h�h�h�h�h�e]�(hRh�h�h�h�h�h�h�h�e]�(hTh�h�h�h�h�h�h�h�e]�(hWh�h�j  j  j  j  j	  j  e]�(hYj  j  j  j  j  j  j  j  e]�(h\j  j!  j#  j%  j'  j)  j+  j-  e]�(h^j0  j2  j4  j6  j8  j:  j<  j>  e]�(hajA  jC  jE  jG  jI  jK  jM  jO  e]�(hcjR  jT  jV  jX  jZ  j\  j^  j`  e]�(hfjc  je  jg  ji  jk  jm  jo  jq  e]�(hhjt  jv  jx  jz  j|  j~  j�  j�  e]�(hkj�  j�  j�  j�  j�  j�  j�  j�  e]�(hmj�  j�  j�  j�  j�  j�  j�  j�  e]�(hpj�  j�  j�  j�  j�  j�  j�  j�  e]�(hrj�  j�  j�  j�  j�  j�  j�  j�  e]�(huj�  j�  j�  j�  j�  j�  j�  j�  e]�(hwj�  j�  j�  j�  j�  j�  j�  j�  e]�(hzj�  j�  j�  j�  j�  j�  j�  j�  e]�(h|j�  j�  j   j  j  j  j  j
  e]�(hj
  j  j  j  j  j  j  j  e]�(h�j  j   j"  j$  j&  j(  j*  j,  e]�(h�j/  j1  j3  j5  j7  j9  j;  j=  eet�bj  KEKFK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j|  �Index���}�(�data�h7h9K ��h;��R�(KKF��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����__instrument_venue__���R�j�  �"__fb_is_created_through_fallback__���R�j�  �__contract_multiplier__���R��id��buySell��date��marketIdentifiers.parties��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��!sellerDecisionMakerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_seller_dec_maker__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� transactionDetails.ultimateVenue��transactionDetails.venue��transactionDetails.price��transactionDetails.priceAverage��transactionDetails.quantity�� transactionDetails.priceCurrency��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity��!orderIdentifiers.transactionRefNo��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��priceFormingData.price��executionDetails.limitPrice��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo��executionDetails.orderType��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��&executionDetails.shortSellingIndicator��+executionDetails.liquidityProvisionActivity��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.tradingDateTime��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��3tradersAlgosWaiversIndicators.shortSellingIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��%transactionDetails.cumulativeQuantity��__asset_class__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__option_strike_price__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�j~  �pandas.core.indexes.range��
RangeIndex���}�(j�  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.