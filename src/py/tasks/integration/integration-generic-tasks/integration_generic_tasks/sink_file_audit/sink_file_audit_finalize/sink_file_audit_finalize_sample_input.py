import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="IT2Yy6EjgiiEkqGTLRRI9",
        name="order_blotter",
        stack="dev-shares-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            sink_file_audit_id="mares8.dev.steeleye.co:aries/ingress/nonstreamed/evented/"
            "order_blotter/UCM3/UBS Aries Test File - Missing IUI_modRec_testing_quarantine"
            ".csv:2025-01-28T18:05:50.049151",
            file_uri="s3://mares8.dev.steeleye.co/aries/ingest/order_blotter/2025/01/28/IT2Yy6EjgiiEkqGTLRRI9/"
            "sink_file_audit_init/e3d1748ecf5a74f88099acb9779fd50cfa842a07d0f79f785527b6219d7e139f___audit"
            ".ndjson",
            conductor_workflow_execution_id="733505e1-2c24-4a3e-89e6-25d1c3b55a4d",
        )
    )
    task = TaskFieldSet(
        id="test_task1", name="sink_file_audit_finalize", version="latest", success=False
    )
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
