# type: ignore
import logging
from benchmark_mode import benchmark
from integration_generic_tasks.integration_generic_tasks_task import integration_generic_tasks_run
from integration_generic_tasks.mymarket.mymarket_bp_person.mymarket_bp_person_sample_input import (
    sample_input,
)

logger = logging.getLogger("mymarket_bp_person")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_generic_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
