import logging
from benchmark_mode import benchmark
from integration_generic_tasks.generic.automated_ingestion_report.automated_ingestion_report_sample_input import (  # noqa E501
    sample_input,
)
from integration_generic_tasks.generic.automated_ingestion_report.automated_ingestion_report_task import (  # noqa E501
    automated_ingestion_report_run,
)
from integration_generic_tasks.integration_generic_tasks_task import integration_generic_tasks_run

logger = logging.getLogger("automated_ingestion_report")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_generic_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
