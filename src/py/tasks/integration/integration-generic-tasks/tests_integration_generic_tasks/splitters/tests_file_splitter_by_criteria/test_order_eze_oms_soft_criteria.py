import boto3
import pandas as pd
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file

from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.static import (
    ORDER_EZE_OMS_SOFT_WORKFLOW_NAME,
)

from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_task import (  # noqa E501
    file_splitter_by_criteria_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from mock.mock import patch
from moto import mock_aws
from pathlib import Path
from se_io_utils.json_utils import read_json
from se_io_utils.tempfile_utils import tmp_directory

CURRENT_PATH = Path(__file__).parent
RESULT_JSON_PATH = CURRENT_PATH.joinpath("result.json")

# EZE OMS Soft test data paths
ORDER_EZE_OMS_SOFT_DATA_PATH = CURRENT_PATH.joinpath("data", "order_eze_oms_soft")
ORDER_EZE_OMS_SOFT_ORDERS_INPUT = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "Test_SteelEye_Orders_050825163237.csv"
)
ORDER_EZE_OMS_SOFT_EXECUTIONS_INPUT = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "Test_SteelEye_Executions_050825162851.csv"
)
ORDER_EZE_OMS_SOFT_ALLOCATIONS_INPUT = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "Test_SteelEye_Allocations_050825162630.csv"
)
# Expected output files (to be created)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_0 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_0_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_1 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_1_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_2 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_2_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_3 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_3_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_4 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_4_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_5 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_5_eze_oms_soft.csv"
)
ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_6 = ORDER_EZE_OMS_SOFT_DATA_PATH.joinpath(
    "expected_split_6_eze_oms_soft.csv"
)
TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestOrderEzeOmsSoftCriteria:
    @staticmethod
    def teardown_method():
        """Deletes temporary files created during the test."""
        if RESULT_JSON_PATH.exists():
            RESULT_JSON_PATH.unlink()

        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @staticmethod
    def s3_add_objects_to_bucket(bucket_name: str, local_file_path: Path, criteria: str):
        """Puts the single input file in the correct path in the mock S3
        bucket.

        :param bucket_name: Bucket name of Mock S3 bucket
        :param local_file_path: File path of the file to be uploaded
        :return: None, uploads files to the mock S3 bucket
        """

        # Create bucket
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=bucket_name)
        base_key = f"aries/ingress/nonstreamed/evented/{criteria}/"
        file_name = Path(local_file_path).name
        with open(local_file_path, "rb") as f:
            s3.put_object(
                Bucket=bucket_name,
                Key=f"{base_key}{file_name}",
                Body=f.read(),
            )

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "max_batch_size": 4,
                "workflow": {"streamed": False, "name": ORDER_EZE_OMS_SOFT_WORKFLOW_NAME},
            },
        ),
    )
    @mock_aws
    def test_order_eze_oms_soft_criteria(
        self,
        mocker,
        sample_aries_task_input_order_eze_oms_soft: AriesTaskInput,
    ):
        self.s3_add_objects_to_bucket(
            local_file_path=ORDER_EZE_OMS_SOFT_ALLOCATIONS_INPUT,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="order_eze_oms_soft",
        )
        self.s3_add_objects_to_bucket(
            local_file_path=ORDER_EZE_OMS_SOFT_EXECUTIONS_INPUT,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="order_eze_oms_soft",
        )
        self.s3_add_objects_to_bucket(
            local_file_path=ORDER_EZE_OMS_SOFT_ORDERS_INPUT,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="order_eze_oms_soft",
        )

        task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_eze_oms_soft
        )

        file_uri_0: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_0")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_1: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_1")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_2: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_2")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_3: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_3")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_4: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_4")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_5: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_5")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        file_uri_6: str = (
            task_result.output_param.params.get("dynamicTaskInputs")
            .get("tr_feed_random_subworkflow_6")
            .get("io_param")
            .get("params")
            .get("file_uri")
        )

        # Assert correct number of batches where created
        assert len(task_result.output_param.params.get("dynamicTasks")) == 7

        assert task_result.output_param.params["dynamicTasks"] == [
            {
                "taskReferenceName": "tr_feed_random_subworkflow_0",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_1",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_2",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_3",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_4",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_5",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
            {
                "taskReferenceName": "tr_feed_random_subworkflow_6",
                "type": "SUB_WORKFLOW",
                "subWorkflowParam": {"name": "tr_feed_random_subworkflow"},
            },
        ]

        assert task_result.output_param.params["dynamicTaskInputs"] == {
            "tr_feed_random_subworkflow_0": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_0,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_1": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_1,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_2": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_2,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_3": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_3,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_4": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_4,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_5": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_5,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
            "tr_feed_random_subworkflow_6": {
                "io_param": {
                    "params": {
                        "source_file_uri": "s3://test_tenant.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
                        "file_uri": file_uri_6,
                    },
                },
                "workflow": {
                    "trace_id": "trace",
                    "start_timestamp": "2024-12-04T00:00:00",
                    "name": "file-splitter-by-criteria",
                    "stack": "dev-shared-2",
                    "tenant": "test_tenant",
                },
            },
        }

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_0",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_0,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_1",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_1,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_2",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_2,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_3",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_3,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_4",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_4,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_5",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_5,
        )

        self.assert_frame(
            task_result=task_result,
            dynamic_task_input="tr_feed_random_subworkflow_6",
            expected_path=ORDER_EZE_OMS_SOFT_EXPECTED_SPLIT_6,
        )

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "max_batch_size": 4,
                "workflow": {"streamed": False, "name": ORDER_EZE_OMS_SOFT_WORKFLOW_NAME},
            },
        ),
    )
    @mock_aws
    def test_order_eze_oms_soft_criteria_missing_file(
        self,
        mocker,
        sample_aries_task_input_order_eze_oms_soft: AriesTaskInput,
    ):
        self.s3_add_objects_to_bucket(
            local_file_path=ORDER_EZE_OMS_SOFT_ALLOCATIONS_INPUT,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="order_eze_oms_soft",
        )
        self.s3_add_objects_to_bucket(
            local_file_path=ORDER_EZE_OMS_SOFT_ORDERS_INPUT,
            bucket_name="test_tenant.dev.steeleye.co",
            criteria="order_eze_oms_soft",
        )

        task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_eze_oms_soft
        )

        assert task_result.output_param.params["dynamicTasks"] == []
        assert task_result.output_param.params["dynamicTaskInputs"] == {}

    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {"lake_prefix": "s3://test.dev.steeleye.co"},
                "max_batch_size": 4,
                "workflow": {"streamed": False, "name": ORDER_EZE_OMS_SOFT_WORKFLOW_NAME},
            }
        ),
    )

    @staticmethod
    def assert_frame(task_result: AriesTaskResult, dynamic_task_input: str, expected_path: Path):
        split_path = task_result.output_param.params["dynamicTaskInputs"][dynamic_task_input][  # type: ignore[union-attr]
            "io_param"
        ]["params"]["file_uri"]

        split_local = run_download_file(file_url=split_path)
        split_df = pd.read_csv(split_local)
        expected_split = pd.read_csv(expected_path)

        pd.testing.assert_frame_equal(split_df, expected_split)


def mock_auditor_upload_side_effect(audit_filepath: str, **kwargs) -> None:
    shutil.copy(Path(audit_filepath), AUDIT_PATH)
