import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_test_utils.mock.mock_imports import mock_imports

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"

mock_imports(mock_retry=True)


@pytest.fixture()
def sample_aries_task_input_tr_bbg_emsi_orders() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test_tenant",
        start_timestamp=datetime(year=2024, month=7, day=8),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test_tenant.dev.steeleye.co/aries/"
            "ingress/nonstreamed/evented/tr_bbg_emsi_orders/input_tr_bbg_emsi_orders_file.csv",
            dynamic_tasks={
                "dynamic_task": {
                    "task_reference_name": "tr_feed_random_subworkflow",
                    "name": "tr_feed_random_subworkflow",
                    "type": "SUB_WORKFLOW",
                }
            },
        )
    )
    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_order_tr_fidessa_eod() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test_tenant",
        start_timestamp=datetime(year=2024, month=12, day=4),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test_tenant.dev.steeleye.co/aries/"
            "ingress/nonstreamed/evented/order_tr_fidessa_eod/ORDER.20241029.psv",
            dynamic_tasks={
                "dynamic_task": {
                    "task_reference_name": "tr_feed_random_subworkflow",
                    "name": "tr_feed_random_subworkflow",
                    "type": "SUB_WORKFLOW",
                }
            },
        )
    )
    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_skippable_exectuions_order_tr_fidessa_eod() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test_tenant",
        start_timestamp=datetime(year=2024, month=12, day=4),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test_tenant.dev.steeleye.co/aries/"
            "ingress/nonstreamed/evented/order_tr_fidessa_eod/Steeleye.trigger",
            dynamic_tasks={
                "dynamic_task": {
                    "task_reference_name": "tr_feed_random_subworkflow",
                    "name": "tr_feed_random_subworkflow",
                    "type": "SUB_WORKFLOW",
                }
            },
        )
    )
    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_order_flextrade_bell_potter_fix() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace",
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test",
        # start timestamp is important, as its used to calculate delta
        # day, modify carefully taking that into account
        start_timestamp=datetime.strptime("2024-11-25T10:00:00.041934", "%Y-%m-%dT%H:%M:%S.%f"),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/ingress/raw/order_flextrade_bell_potter_fix/",
            delta_in_days=0,
            timezone="Australia/Sydney",
            chunk_size=30,
            dynamic_tasks={
                "dynamic_task": {
                    "name": "order_flextrade_bell_potter_fix_subworkflow",
                    "task_reference_name": "order_flextrade_bell_potter_fix_subworkflow",
                    "type": "SUB_WORKFLOW",
                },
            },
        )
    )

    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_order_flextrade_bell_potter_fix_replace_logic() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace2",
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test",
        # start timestamp is important, as its used to calculate delta
        # day, modify carefully taking that into account
        start_timestamp=datetime.strptime("2024-11-25T10:00:00.041934", "%Y-%m-%dT%H:%M:%S.%f"),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/ingress/raw/order_flextrade_bell_potter_fix/replace_logic/",
            dynamic_tasks={
                "dynamic_task": {
                    "name": "order_flextrade_bell_potter_fix_subworkflow",
                    "task_reference_name": "order_flextrade_bell_potter_fix_subworkflow",
                    "type": "SUB_WORKFLOW",
                },
            },
        )
    )

    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_order_iress_bell_potter_fix() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="trace",
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test",
        # start timestamp is important, as its used to calculate delta
        # day, modify carefully taking that into account
        start_timestamp=datetime.strptime("2024-10-05T17:00:00.041934", "%Y-%m-%dT%H:%M:%S.%f"),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/ingress/raw/order_iress_bell_potter_fix/",
            delta_in_days=1,
            timezone="Australia/Sydney",
            dynamic_tasks={
                "dynamic_task": {
                    "name": "order_iress_bell_potter_fix_subworkflow",
                    "task_reference_name": "order_iress_bell_potter_fix_subworkflow",
                    "type": "SUB_WORKFLOW",
                },
            },
        )
    )

    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_order_eze_oms_soft() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="file-splitter-by-criteria",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(year=2024, month=12, day=4),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/raw/order_eze_oms_soft/Test_SteelEye_Orders_050825163237.csv",
            dynamic_tasks={
                "dynamic_task": {
                    "name": "order_eze_oms_soft_subworkflow",
                    "task_reference_name": "order_eze_oms_soft_subworkflow",
                    "type": "SUB_WORKFLOW",
                },
            },
        )
    )
    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
