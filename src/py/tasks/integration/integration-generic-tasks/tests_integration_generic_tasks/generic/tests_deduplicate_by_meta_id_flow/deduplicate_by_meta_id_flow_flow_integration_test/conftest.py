import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_deduplicate_by_meta_id",
        name="deduplicate_by_meta_id",
        stack="dev-blue",
        tenant="test",
        start_timestamp="2023-07-19T17:28:00.000000+00:00",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="",
            data_model="se_elastic_schema.models.tenant.communication.email:Email",
        )
    )
    task = TaskFieldSet(name="deduplicate_by_meta_id_flow", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
