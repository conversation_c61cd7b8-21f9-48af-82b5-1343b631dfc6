���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager����pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�`             �      �      �      �      �      �      �      �      �      �      �      �?��numpy��dtype����f8�����R�(K�<�NNNJ����J����K t�bKK���C�t�R�h(�                            �h�i8�����R�(KhNNNJ����J����K t�bK��ht�R�K��R�hh(�                      	       
       �hKK��ht�R��builtins��slice���KKK��R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KKK��h�O8�����R�(K�|�NNNJ����J����K?t�b�]�(�$2b6463e1-2448-4c28-910b-44f64e8a8f4c��$52910bd5-9c97-46e0-9d80-50c6ac4f94b8��$7d3e6c6d-4d6e-4319-ae8e-44a3f4adbd2a��$8506359c-834b-4e23-985d-2568e3d0c4f2�]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]�}�(�label��BBG��id��s5545020�ua]�}�(�label��BBG��id��m0692364�ua]�}�(�label��BBG��id��a4227855�ua]�}�(�label��BBG��id��p0287923�ua]�}�(�label��MOBILE��number��************�ua]�(}�(�label��WORK��number��
**********�u}�(�label��MOBILE��number��************�ue]�}�(�label��MOBILE��number��************�ua]�(}�(�label��WORK��number��
**********�u}�(�label��MOBILE��number��************�ue�Balyasny Asset Management��Morgan Stanley��HSBC��Morgan Stanley��
AccountPerson��
AccountPerson��
AccountPerson��
AccountPerson��Misty Waters��Chuck Wagon��Buster Hyman��Robin Banks��Misty��Chuck��Buster��Robin��Waters��Wagon��Hyman��Banks�]��UK�a]��ES�a]��GB�a]��CN�a]�(}�(�id��Misty_Waters��label��id�u}�(�id��Misty_Waters��label��account�u}�(�id��asrxn��label��id�u}�(�id��asrxn��label��account�ue]�(}�(�id��Audrie_Aspy��label��id�u}�(�id��Audrie_Aspy��label��account�u}�(�id��asmxv��label��id�u}�(�id��asmxv��label��account�ue]�(}�(�id��Buster_Hyman��label��id�u}�(�id��Buster_Hyman��label��account�ue]�(}�(�id��Robin_Banks��label��id�u}�(�id��Robin_Banks��label��account�u}�(�id��astxt��label��id�u}�(�id��astxt��label��account�ue��/Users/<USER>/projects/se-mono/src/py/tasks/integration/aries/mymarket/mymarket-universal-steeleye-person/tests_mymarket_universal_steeleye_person/data/valid_executions/scenario_aws/mymarket_universal_steeleye_person.csv���/Users/<USER>/projects/se-mono/src/py/tasks/integration/aries/mymarket/mymarket-universal-steeleye-person/tests_mymarket_universal_steeleye_person/data/valid_executions/scenario_aws/mymarket_universal_steeleye_person.csv���/Users/<USER>/projects/se-mono/src/py/tasks/integration/aries/mymarket/mymarket-universal-steeleye-person/tests_mymarket_universal_steeleye_person/data/valid_executions/scenario_aws/mymarket_universal_steeleye_person.csv���/Users/<USER>/projects/se-mono/src/py/tasks/integration/aries/mymarket/mymarket-universal-steeleye-person/tests_mymarket_universal_steeleye_person/data/valid_executions/scenario_aws/mymarket_universal_steeleye_person.csv��
Equity Income��Dealing - Global Dealing Desk��
Global Credit��Dealing - Global Dealing Desk�]�}�(�id��XWL��name��XWL�ua]�}�(�id��Q7N��name��Q7N�ua]�}�(�id��GB7��name��GB7�ua]�}�(�id��KCS��name��KCS�uaN�Execution Trader��Broker��Research Analyst�]�(�id:misty_waters��************��s5545020��<EMAIL>��account:misty_waters��id:asrxn��
account:asrxn�e]�(�
account:asmxv��m0692364��
**********��id:asmxv��id:audrie_aspy��************��account:audrie_aspy��<EMAIL>�e]�(�************��a4227855��id:buster_hyman��<EMAIL>��account:buster_hyman�e]�(�id:robin_banks��
account:astxt��p0287923��
**********��account:robin_banks��id:astxt��************��<EMAIL>�eNNN]�(�csurv��tsurv�eNNN�EU�NNN�Medium�]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�aet�bh(��                                                                	       
              
                                                               �hK��ht�R�K��R���]�(�pandas.core.indexes.base��
_new_Index���jF  �Index���}�(�data�h3h5K ��h7��R�(KK��h=�]�(�&id��communications.emails��communications.imAccounts��communications.phoneNumbers��counterparty.name��__meta_model__��name��!officialIdentifiers.branchCountry��personalDetails.firstName��personalDetails.lastName��personalDetails.nationality��personalDetails.dob��$sinkIdentifiers.tradeFileIdentifiers��	sourceKey��structure.department��structure.desks��structure.role��	uniqueIds��monitoring.isMonitoredFor��monitoring.jurisdictionRegion��monitoring.risk��monitoring.isMonitored��	__email__��__matched_feed_index__�et�b�name�Nu��R�jH  jJ  }�(jL  h(�                                     �hK��ht�R�jk  Nu��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.