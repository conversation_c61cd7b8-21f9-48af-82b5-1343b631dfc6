import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
# This must be lower than the default batch size defined in the flow to test the batching logic
os.environ["BATCH_SIZE"] = "10"


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="avatrade_client_data_handler",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(year=2024, month=4, day=4),
        trace_id="trace_id",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/avatrade_client_data_handler/2024/04/04/input.csv",  # noqa E501
            dynamic_tasks=dict(
                market_person=dict(
                    name="elastic_ingestion",
                    task_reference_name="elastic_ingestion_ref",
                    type="SUB_WORKFLOW",
                ),
            ),
        )
    )

    task = TaskFieldSet(
        name="avatrade_client_data_handler_transform", version="latest", success=False
    )

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def person_ids_scroll_result() -> pd.DataFrame:
    return pd.DataFrame()
