from aries_task_link.models import AriesTaskInput
from pathlib import Path
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from surveillance_utils.test_mock_helpers import (
    data_lake,
    dump_file_into_datalake,
    dump_files_into_datalake,
)

EMAIL_INGRESS_DIR = "./aries/ingest/email/2023/06/12/A_TRACE_ID/B_STACK__email_updater/"

TEST_PATH = Path(__file__).parent.joinpath("data")


def fake_bulk_update(*args, **kwargs):
    emails = kwargs.pop("docs")

    for email in emails:
        # check only &ID and analytics are in
        assert len(email.keys()) == 2

        # check classifier update from class_ to class
        if email["analytics"].get("classifier", None) is not None:
            assert all("class_" not in k for k in email["analytics"]["classifier"]["predictions"])

        # check zoning update from class_ to class
        if email["analytics"].get("zoning", None) is not None:
            assert all("class_" not in k for k in email["analytics"]["zoning"]["predictionOffsets"])

    return


def fake_aries_task_input(
    input_file_updated: str | None, input_file_dropped: str | None, input_file_both: str | None
) -> AriesTaskInput:
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "1685806839",
                "name": "email-workflow",
                "stack": "local",
                "tenant": "foxtrot",
            },
            "task": {"name": "email-updater-task", "version": "", "success": True},
            "input_param": {
                "params": {
                    "update_alerted": input_file_updated,
                    "update_dropped": input_file_dropped,
                    "update_both": input_file_both,
                    "watch_execution_id": "fake_watch",
                }
            },
        }
    )


def test_run_email_updater_classifier_and_zoner(monkeypatch, fake_es_repo_instance):
    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)
    monkeypatch.setattr(SlimRecordHandler, "bulk_update", fake_bulk_update)

    from intelligence_core_tasks.email_updater.main import run_email_updater

    test_data_paths = [
        TEST_PATH.joinpath("update_both_test_file.ndjson").as_posix(),
        TEST_PATH.joinpath("update_dropped_test_file.ndjson").as_posix(),
    ]

    with data_lake():
        update_both, update_dropped = dump_files_into_datalake(EMAIL_INGRESS_DIR, test_data_paths)

        aries_task_input = fake_aries_task_input(None, update_dropped, update_both)

        output = run_email_updater(aries_task_input)

        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("input_count")
            == 8
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("skipped_count")
            == 0
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("updated_count")
            == 8
        )


def test_run_email_updater_only_zoner(monkeypatch, fake_es_repo_instance):
    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)
    monkeypatch.setattr(SlimRecordHandler, "bulk_update", fake_bulk_update)

    from intelligence_core_tasks.email_updater.main import run_email_updater

    test_data_path = TEST_PATH.joinpath("update_both_test_file.ndjson").as_posix()

    with data_lake():
        update_both = dump_file_into_datalake(EMAIL_INGRESS_DIR, test_data_path)

        aries_task_input = fake_aries_task_input(None, None, update_both)

        output = run_email_updater(aries_task_input)

        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("input_count")
            == 4
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("skipped_count")
            == 0
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("updated_count")
            == 4
        )


def test_run_email_updater_only_classifier(monkeypatch, fake_es_repo_instance):
    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)
    monkeypatch.setattr(SlimRecordHandler, "bulk_update", fake_bulk_update)

    from intelligence_core_tasks.email_updater.main import run_email_updater

    test_data_paths = [
        TEST_PATH.joinpath("update_alerted_test_file.ndjson").as_posix(),
        TEST_PATH.joinpath("update_dropped_test_file.ndjson").as_posix(),
    ]

    with data_lake():
        update_alerted, update_dropped = dump_files_into_datalake(
            EMAIL_INGRESS_DIR, test_data_paths
        )

        aries_task_input = fake_aries_task_input(update_alerted, update_dropped, None)

        output = run_email_updater(aries_task_input)

        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("input_count")
            == 8
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("skipped_count")
            == 0
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("updated_count")
            == 8
        )


def test_run_fail(monkeypatch, fake_es_repo_instance):
    monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_repo_instance)
    monkeypatch.setattr(SlimRecordHandler, "bulk_update", fake_bulk_update)

    from intelligence_core_tasks.email_updater.main import run_email_updater

    test_data_paths = [
        TEST_PATH.joinpath("update_alerted_test_file.ndjson").as_posix(),
        TEST_PATH.joinpath("file_with_one_fail_update.ndjson").as_posix(),
    ]

    with data_lake():
        update_alerted, update_dropped = dump_files_into_datalake(
            EMAIL_INGRESS_DIR, test_data_paths
        )

        aries_task_input = fake_aries_task_input(update_alerted, update_dropped, None)

        output = run_email_updater(aries_task_input)

        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("input_count")
            == 9
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("skipped_count")
            == 1
        )
        assert (
            output.app_metric.metrics.get("custom").get("email-updater-task").get("updated_count")
            == 8
        )
