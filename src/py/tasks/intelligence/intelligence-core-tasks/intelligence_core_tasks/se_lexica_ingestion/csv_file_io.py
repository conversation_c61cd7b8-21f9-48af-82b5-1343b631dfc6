import fsspec
import logging
import os
import pandas as pd
from datetime import datetime
from intelligence_core_tasks.se_lexica_ingestion.config import LEXICA_INGESTION_CONFIG
from intelligence_core_tasks.se_lexica_ingestion.static import (
    FILENAME_SEPARATOR_PATTERN,
    FILENAME_TIMESTAMP_PATTERN,
    MASTER_DATA_LEXICA_KEY,
)
from typing import Optional
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


def read_csv(filepath: str) -> pd.DataFrame:
    """Reads CSV file to a pandas dataframe, filepath in local/s3/azure is
    specified in flow input.

    :param filepath: Path to the csv file
    :type filepath: str
    :raises Exception: Informative errors while reading the file
    :return: The content of the csv file as a pandas dataframe
    :rtype: pd.DataFrame
    """
    # Check filepath is csv
    if not filepath.endswith(".csv"):
        raise Exception(f"filepath is not an csv: {filepath}")

    try:
        records = pd.read_csv(filepath)
    except IsADirectoryError:
        logger.exception("Failed to read file. Destination path is missing file name.")
        raise Exception(f"Failed to read file. Destination path is missing file name: {filepath}")
    except Exception as e:
        logger.exception(e, exc_info=True)
        raise Exception(f"an error occurred while fetching: {filepath}")

    return records


def write_csv(
    source_dataframe: pd.DataFrame,
    lexica_file_name: str,
    previous_version: int,
    timestamp: Optional[str] = None,
) -> str:
    """Write the lexica files as csv to the defined S3 bucket.

    :param source_dataframe: dataframe to save to csv
    :param lexica_file_name: name of the lexica file
    :param previous_version: previous version number
    :param timestamp: timestamp to use
    :return: timestamp
    """
    s3_bucket = LEXICA_INGESTION_CONFIG.get_bucket

    if timestamp is None:
        timestamp = datetime.now().strftime(FILENAME_TIMESTAMP_PATTERN)

    version = "v" + str(previous_version + 1)
    filename = f"{lexica_file_name}.csv"

    destination_key = (
        f"{MASTER_DATA_LEXICA_KEY}/{timestamp}{FILENAME_SEPARATOR_PATTERN}{version}/{filename}"
    )

    output_filepath = urlparse(f"{s3_bucket}{destination_key}").geturl()

    try:
        fs, _, _ = fsspec.get_fs_token_paths(output_filepath)
    except ValueError:
        raise Exception(f"ValueError for {output_filepath}")

    logger.info(f"Storing csv content in {output_filepath}")

    # Needed to recursively create file directory locally
    if not fs.exists(os.path.dirname(output_filepath)):
        fs.makedirs(os.path.dirname(output_filepath))

    source_dataframe.to_csv(output_filepath, index=False, encoding="utf-8", sep=",")

    logger.info(f"{lexica_file_name} file stored successfully.")

    return timestamp
