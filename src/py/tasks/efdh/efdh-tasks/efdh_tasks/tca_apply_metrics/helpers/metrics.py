import gc
import logging
import polars as pl
from efdh_tasks.tca_apply_metrics.tca_apply_metrics_config import tca_apply_metrics_settings
from market_data_utils.schema.parquet import EoDStatsColumns, QuoteTickColumns, TradeTickColumns
from se_schema.all_enums import BuySellIndicator, TCAFlagStatus
from tca_utils.schema.order import PriceAuditing, TCAFlagBoolColumns
from tca_utils.schema.tca_market_data import (
    MarketDataAsofJoinData,
    QuoteTickColumnsRenamed,
    TickColumns,
    TradeTickColumnsRenamed,
)
from tca_utils.schema.tca_metrics import (
    Alias,
    CostDetailsColumns,
    ElasticMetaColumns,
    ElasticOrderFields,
    ExecutionTsIntervals,
    PrevalArgs,
    PriceColumns,
    RecordFlags,
    TCAMarketData,
)
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class TradeMetrics:
    def execute(
        self,
        market_data: TCAMarketData,
        frame: pl.LazyFrame,
        ric_venue: Optional[str] = None,
    ) -> pl.LazyFrame:
        """

        :param market_data:
        :param frame:
        :return:
        """
        # set RIC
        logger.info(f"Metrics application started for RIC: {market_data.ric}")
        if Alias.RIC in frame.collect_schema().names():
            frame = frame.with_columns(
                pl.when(pl.col(Alias.RIC).is_not_null())
                .then(pl.col(Alias.RIC))
                .otherwise(pl.lit(None))
                .alias(ElasticOrderFields.RIC)
            )

        if market_data.no_market_data:
            frame = frame.with_columns(pl.lit(True).alias(TCAFlagBoolColumns.MISSING_MARKET_DATA))

        data_predicate = (
            pl.col(TCAFlagBoolColumns.MISSING_RIC)
            | pl.col(TCAFlagBoolColumns.INVALID_CURRENCY)
            | pl.col(TCAFlagBoolColumns.CURRENCY_MISMATCH)
            | pl.col(TCAFlagBoolColumns.MISSING_MARKET_DATA)
            | pl.col(TCAFlagBoolColumns.DATA_INTEGRITY)
        )
        flagged_data = frame.filter(data_predicate)

        frame = frame.filter(data_predicate.not_())

        if not flagged_data.collect().is_empty():
            flagged_data = flagged_data.with_columns(
                pl.when(pl.col(TCAFlagBoolColumns.INVALID_CURRENCY))
                .then([TCAFlagStatus.INVALID_CURRENCY, TCAFlagStatus.MISS])
                .when(pl.col(TCAFlagBoolColumns.CURRENCY_MISMATCH))
                .then([TCAFlagStatus.CURRENCY_MISMATCH, TCAFlagStatus.MISS])
                .when(pl.col(TCAFlagBoolColumns.MISSING_MARKET_DATA))
                .then([TCAFlagStatus.MISSING_MARKET_DATA, TCAFlagStatus.MISS])
                .when(pl.col(TCAFlagBoolColumns.DATA_INTEGRITY))
                .then([TCAFlagStatus.DATA_INTEGRITY, TCAFlagStatus.MISS])
                .otherwise(pl.lit(None))
                .alias(RecordFlags.TCA_STATUS)
            )

        if frame.collect().is_empty():
            return flagged_data

        # Asof joins
        join_functions = [
            self._join_asof_market_data,
            self._join_asof_order_received,
            self._join_asof_order_submitted,
            self._join_asof_preval_quotes,
            self._join_asof_preval_stats,
            self._executed_before_open,
        ]
        for func in join_functions:
            try:
                frame = func(  # type: ignore
                    frame=frame,
                    market_data=market_data,
                )

            except Exception as e:
                logger.exception("Error joining frames")
                logger.exception(e)
        result: Dict[Any, Any] = {}

        for func in [
            self._assign_arrival_price,
            self._assign_ask_price,
            self._assign_bid_price,
            self._assign_touch,
            self._assign_mid_point_price,
            self._assign_market_price,
            self._assign_close_price,
            self._assign_open_price,
            self._assign_mid_arrival_price,
            self._assign_first_fill_arrival_price,
            self._assign_market_submission_arrival,
            self._assign_market_day_traded_volume,
            self._assign_near_touch,
            self._limit_vs_execution,
            self._assign_market_avg_daily_volume,
            self._assign_price_volatility,
            self._assign_volume_volatility,
            self._assign_vwap,
            self._assign_vwap_limit_adjusted,
            self._assign_vwap_start_to_close,
            self._assign_vwap_start_to_close_limit_adjusted,
            self._assign_vwap_submit_to_execution,
            self._assign_vwap_start_to_execution,
        ]:
            try:
                logger.info(f"Calculating {func.__name__}")
                frame = func(
                    frame=frame,  # type: ignore
                    trades=market_data.trades.collect(),
                    stats=market_data.stats,
                    result=result,
                    ric_venue=ric_venue,
                )
            except Exception as e:
                logger.warning(f"Error processing metric function: {func.__name__}")
                logger.exception(e)
                # TODO: Assign list to market price audit column
                if func.__name__ in ["_assign_market_price"]:
                    frame = frame.with_columns(pl.lit(None).alias(RecordFlags.TCA_STATUS))
                    predicate = pl.col(RecordFlags.TCA_STATUS).is_null()

                    frame = frame.with_columns(
                        pl.when(predicate)
                        .then([TCAFlagStatus.UNHANDLED, TCAFlagStatus.MISS])
                        .otherwise(pl.col(RecordFlags.TCA_STATUS))
                        .alias(RecordFlags.TCA_STATUS)
                    )
                    continue
        del market_data
        gc.collect()

        logger.info("Calculating preval quotes metrics")
        for args in PrevalArgs.QUOTES_ARGS:
            frame = self._assign_preval_quotes(frame=frame, **args)
        logger.info("Calculating preval stats metrics")
        for args in PrevalArgs.STATS_ARGS:
            frame = self._assign_preval_stats(frame=frame, **args)

        if not flagged_data.collect().is_empty():
            logger.info("Appending unprocessable records back onto frame")
            frame = pl.concat([frame, flagged_data], how="diagonal")

        return frame

    def _join_asof_market_data(
        self, frame: pl.LazyFrame, market_data: TCAMarketData, **kwargs
    ) -> pl.LazyFrame:
        """Joins orders onto market data frames on nearest historical
        timestamps. Both frames need to be sorted in asc order for this to
        function.

        :param frame:
        :param market_data:
        :return:
        """
        logger.info(
            f"Joining nearest historical market data for ric: {market_data.ric} onto orders."
        )
        frame = frame.sort(Alias.TRADING_DATE_TIME)
        if not market_data.quotes.collect().is_empty():
            quotes = market_data.quotes.select(
                MarketDataAsofJoinData.REQUIRED_COLUMNS_QUOTES
            ).rename(MarketDataAsofJoinData.QUOTE_COLUMNS_EXECUTION_TS_RENAME)
            logger.info("Joining nearest historical quotes onto working order frame.")

            # for touch price in the cases of duplicate timestamps we need to select the highest Ask
            # and the lowest bid
            buy_quotes = quotes.sort(
                [
                    QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
                    QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
                ]
            ).unique(
                subset=[QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME],
                keep="last",
                maintain_order=True,
            )

            sell_quotes = quotes.sort(
                [
                    QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
                    QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
                ]
            ).unique(
                maintain_order=True,
                subset=[QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME],
                keep="first",
            )

            sell_frame = frame.filter(pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value]))
            buy_frame = frame.filter(pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value]))

            sell_frame = sell_frame.join_asof(
                sell_quotes,
                left_on=Alias.TRADING_DATE_TIME,
                right_on=QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )
            buy_frame = buy_frame.join_asof(
                buy_quotes,
                left_on=Alias.TRADING_DATE_TIME,
                right_on=QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )
            del buy_quotes, sell_quotes

            frame = pl.concat([buy_frame, sell_frame], how="diagonal").sort(Alias.TRADING_DATE_TIME)

            logger.info("Quotes backward asof join complete")

        if not market_data.trades.collect().is_empty():
            trades = (
                market_data.trades.select(MarketDataAsofJoinData.TRADE_COLUMNS)
                .rename(MarketDataAsofJoinData.TRADE_COLUMN_EXECUTION_TS_RENAME)
                .unique(
                    subset=[TradeTickColumnsRenamed.EXECUTION_TS_DATE_TIME], maintain_order=True
                )
            ).sort(TradeTickColumnsRenamed.EXECUTION_TS_DATE_TIME)

            logger.info("Joining nearest historical trades onto working order frame.")
            frame = frame.join_asof(
                trades,
                left_on=Alias.TRADING_DATE_TIME,
                right_on=TradeTickColumnsRenamed.EXECUTION_TS_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )
            logger.info("Trades backward asof join complete")

        if not market_data.stats.collect().is_empty():
            logger.info("Stats Join onto working order frame")
            frame = frame.join(
                market_data.stats.select(MarketDataAsofJoinData.STATS_COLUMNS),
                left_on=Alias.DATE,
                right_on=EoDStatsColumns.DATE,
                how="left",
            )
            logger.info("Stats join complete")

        return frame

    @staticmethod
    def _join_asof_order_received(
        frame: pl.LazyFrame, market_data: TCAMarketData, **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :param quotes:
        :return:
        """
        logger.info("Joining nearest historical orderReceived onto quotes")
        quotes = market_data.quotes
        trades = market_data.trades
        if trades.collect().is_empty() and quotes.collect().is_empty():
            return frame

        frame = frame.sort(Alias.ORDER_RECEIVED)
        if not quotes.collect().is_empty():
            quotes = quotes.select(MarketDataAsofJoinData.REQUIRED_COLUMNS_QUOTES).rename(
                MarketDataAsofJoinData.QUOTE_COLUMNS_ORDER_RECEIVED_RENAME
            )

            frame = frame.join_asof(
                quotes,
                left_on=Alias.ORDER_RECEIVED,
                right_on=QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_QUOTES_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )

        if not trades.collect().is_empty():
            trades = trades.rename(
                MarketDataAsofJoinData.TRADES_COLUMN_ORDER_RECEIVED_RENAME
            ).select(MarketDataAsofJoinData.TRADE_COLUMNS_ORDER_RECEIVED)
            frame = frame.join_asof(
                trades,
                left_on=Alias.ORDER_RECEIVED,
                right_on=TradeTickColumnsRenamed.ORDER_RECEIVED_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )
        logger.info("Quotes orderReceived asof joins complete")
        return frame

    @staticmethod
    def _join_asof_order_submitted(
        frame: pl.LazyFrame, market_data: TCAMarketData, **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :param quotes:
        :return:
        """
        logger.info("Join nearest historical orderSubmitted onto quotes")
        quotes = market_data.quotes
        if not quotes.collect().is_empty():
            quotes = quotes.select(MarketDataAsofJoinData.REQUIRED_COLUMNS_QUOTES).rename(
                MarketDataAsofJoinData.QUOTE_COLUMNS_ORDER_SUBMITTED_RENAME
            )
            null_order_submitted_frame = frame.filter(
                pl.col(Alias.PARENT_ORDER_SUBMITTED).is_null()
            )
            not_null_order_submitted_frame = frame.filter(
                pl.col(Alias.PARENT_ORDER_SUBMITTED).is_not_null()
            ).sort(Alias.PARENT_ORDER_SUBMITTED)

            not_null_order_submitted_frame = not_null_order_submitted_frame.join_asof(
                quotes,
                left_on=Alias.PARENT_ORDER_SUBMITTED,
                right_on=QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_QUOTES_DATE_TIME,
                strategy="backward",
                tolerance=tca_apply_metrics_settings.TOLERANCE * 1e9,
            )
            frame = pl.concat(
                [null_order_submitted_frame, not_null_order_submitted_frame],
                how="diagonal",
            )
            del null_order_submitted_frame, not_null_order_submitted_frame
        logger.info("orderSubmitted asof join complete")
        return frame

    def _join_asof_preval_quotes(
        self, frame: pl.LazyFrame, market_data: TCAMarketData, **kwargs
    ) -> pl.LazyFrame:
        """Joins orders onto market data frames on nearest historical
        timestamps. Both frames need to be sorted in asc order for this to
        function.

        :param frame:
        :param market_data:
        :return:
        """
        try:
            logger.info("Joining nearest historical preval intervals for quotes")

            quotes = market_data.quotes
            if quotes.collect().is_empty():
                return frame

            frame = frame.sort(Alias.TRADING_DATE_TIME)

            # intervals in seconds -- 1e9 is 1 second in nanoseconds
            second_intervals = {
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_MIN: 60 * 1e9,
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_5_MIN: 60 * 5 * 1e9,
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_10_MIN: 60 * 10 * 1e9,
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_HOUR: 60 * 60 * 1e9,
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_2_HOUR: 60 * 120 * 1e9,
            }

            # create columns with t minus intervals
            frame = frame.with_columns(
                [
                    (pl.col(Alias.TRADING_DATE_TIME) - interval).cast(pl.Int64).alias(column)
                    for column, interval in second_intervals.items()
                ]
            )
            # Asof join renamed quotes frame onto order frame on new t
            # minus columns and renamed date-time in quotes
            rename_interval_map = {
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_MIN: {
                    MarketDataAsofJoinData.RENAME_MAP: MarketDataAsofJoinData.MINUS_ONE_MIN_RENAME,
                    MarketDataAsofJoinData.TOLERANCE: 60 * 5 * 1e9,
                },
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_5_MIN: {
                    MarketDataAsofJoinData.RENAME_MAP: MarketDataAsofJoinData.MINUS_FIVE_MIN_RENAME,
                    MarketDataAsofJoinData.TOLERANCE: 60 * 10 * 1e9,
                },
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_10_MIN: {
                    MarketDataAsofJoinData.RENAME_MAP: MarketDataAsofJoinData.MINUS_TEN_MIN_RENAME,
                    MarketDataAsofJoinData.TOLERANCE: 60 * 60 * 1e9,
                },
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_HOUR: {
                    MarketDataAsofJoinData.RENAME_MAP: MarketDataAsofJoinData.MINUS_ONE_HOUR_RENAME,
                    MarketDataAsofJoinData.TOLERANCE: 60 * 120 * 1e9,
                },
                ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_2_HOUR: {
                    MarketDataAsofJoinData.RENAME_MAP: MarketDataAsofJoinData.MINUS_TWO_HOUR_RENAME,
                    MarketDataAsofJoinData.TOLERANCE: 60 * 240 * 1e9,
                },
            }
            for join_key, join_data in rename_interval_map.items():
                renamed_quotes = quotes.select(
                    MarketDataAsofJoinData.REQUIRED_COLUMNS_QUOTES
                ).rename(
                    join_data[MarketDataAsofJoinData.RENAME_MAP]  # type: ignore
                )
                sorted_join_key = join_data[MarketDataAsofJoinData.RENAME_MAP][  # type: ignore
                    QuoteTickColumns.DATE_TIME
                ]

                # TODO: Check this is actually sorted. If we sort
                #  originally on trading date time and then join on
                # the join key which is trading date time minus the
                # preval epoch then it should still be sorted
                frame = frame.set_sorted(join_key).join_asof(
                    renamed_quotes,
                    left_on=join_key,
                    right_on=sorted_join_key,
                    strategy="backward",
                    tolerance=join_data[MarketDataAsofJoinData.TOLERANCE],  # type: ignore
                )
        except Exception as e:
            logger.exception(e)

        logger.info("Nearest historical preval intervals for quotes complete")
        return frame

    def _join_asof_preval_stats(
        self, frame: pl.LazyFrame, market_data: TCAMarketData
    ) -> pl.LazyFrame:
        """

        :param frame:
        :param stats:
        :return:
        """
        logger.info("Joining nearest historical stats preval onto working order frame")
        stats = market_data.stats

        if stats.collect().is_empty():
            return frame

        day_intervals = {
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_DAY: 1,
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_5_DAYS: 5,
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_10_DAYS: 10,
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_20_DAYS: 20,
        }
        # frame = frame.collect()
        # create t minus n business days columns in order frame
        for column_name, days in day_intervals.items():
            frame = frame.with_columns(
                pl.col(Alias.DATE).dt.add_business_days(-days, roll="forward").alias(column_name)
            )

        # Asof joins renamed stats onto order frame on new t minus date column and
        # renamed stats date column
        rename_stat_interval_map = {
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_1_DAY: MarketDataAsofJoinData.PREVAL_ONE_DAY_RENAME,  # noqa: E501
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_5_DAYS: MarketDataAsofJoinData.PREVAL_FIVE_DAY_RENAME,  # noqa: E501
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_10_DAYS: MarketDataAsofJoinData.PREVAL_TEN_DAY_RENAME,  # noqa: E501
            ExecutionTsIntervals.TRADING_DATE_TIME_MINUS_20_DAYS: MarketDataAsofJoinData.PREVAL_TWENTY_DAY_RENAME,  # noqa: E501
        }

        for join_key, rename_map in rename_stat_interval_map.items():
            renamed_stats = stats.select(MarketDataAsofJoinData.PREVAL_STATS_COLUMNS).rename(
                rename_map
            )
            frame = frame.join(
                renamed_stats,
                left_on=join_key,
                right_on=rename_map.get(EoDStatsColumns.DATE),
                how="left",
            )

        return frame

    @staticmethod
    def _executed_before_open(
        frame: pl.LazyFrame, market_data: TCAMarketData, **kwargs
    ) -> pl.LazyFrame:
        """Checks the date-time of the nearest historical quote or trade is
        executed on the same day.

        :param frame:
        :return:
        """
        logger.info("Calculating which timestamps were executed after the open")
        trades = market_data.trades
        quotes = market_data.quotes

        frame = frame.with_columns(
            [
                pl.lit(False).alias(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN),
                pl.lit(False).alias(PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN),
                pl.lit(False).alias(PriceAuditing.TRADES_EXECUTED_BEFORE_OPEN),
            ]
        )

        if {TradeTickColumns.DATE_TIME, TickColumns.DATE}.issubset(trades.collect_schema().names()):
            min_trades = trades.group_by(by=TickColumns.DATE).agg(
                pl.col(TradeTickColumns.DATE_TIME).min().alias(Alias.MIN_TRADE_TIMESTAMP)
            )
            frame = frame.join(min_trades, left_on=Alias.DATE, right_on="by", how="left")
            frame = frame.with_columns(
                pl.when((pl.col(Alias.TRADING_DATE_TIME) < pl.col(Alias.MIN_TRADE_TIMESTAMP)))
                .then(pl.lit(True))
                .otherwise(pl.lit(False))
                .alias(PriceAuditing.TRADES_EXECUTED_BEFORE_OPEN)
            )

            frame = frame.drop(Alias.MIN_TRADE_TIMESTAMP)
        if {QuoteTickColumns.DATE_TIME, TickColumns.DATE}.issubset(quotes.collect_schema().names()):
            min_quotes = quotes.group_by(by=TickColumns.DATE).agg(
                pl.col(QuoteTickColumns.DATE_TIME).min().alias(Alias.MIN_QUOTE_TIMESTAMP)
            )
            frame = frame.join(min_quotes, left_on=Alias.DATE, right_on="by", how="left")
            frame = frame.with_columns(
                pl.when((pl.col(Alias.TRADING_DATE_TIME) < pl.col(Alias.MIN_QUOTE_TIMESTAMP)))
                .then(pl.lit(True))
                .otherwise(pl.lit(False))
                .alias(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN)
            )

            frame = frame.drop(Alias.MIN_QUOTE_TIMESTAMP)
            frame = frame.join(
                min_quotes,
                left_on=Alias.ORDER_RECEIVED_DATE,
                right_on="by",
                how="left",
            )

            frame = frame.with_columns(
                pl.when((pl.col(Alias.ORDER_RECEIVED) < pl.col(Alias.MIN_QUOTE_TIMESTAMP)))
                .then(pl.lit(True))
                .otherwise(pl.lit(False))
                .alias(PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN)
            )

            frame = frame.drop(Alias.MIN_QUOTE_TIMESTAMP)
        return frame

    def _assign_arrival_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """USES ORDER RECEIVED.

        :param frame:
        :return:
        """

        if not set(
            [
                QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_ASK_PRICE,
                QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_BID_PRICE,
                QuoteTickColumnsRenamed.ORDER_RECEIVED_ASK_SIZE,
                QuoteTickColumnsRenamed.ORDER_RECEIVED_BID_SIZE,
                QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_QUOTES_DATE_TIME,
                PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN,
            ]
        ).issubset(frame.collect_schema().names()):
            logger.warning("COLUMNS MISSING FROM FRAME. CANNOT GENERATE ARRIVAL PRICE.")
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN).not_()
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])
        metric_column = PriceColumns.ARRIVAL_PRICE
        frame = frame.with_columns(
            [
                # arrival price
                pl.when(buy_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_ASK_PRICE))
                .when(sell_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_BID_PRICE))
                .otherwise(pl.lit(None))
                .alias(metric_column),
                # order/arrival time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.ORDER_RECEIVED)
                    - pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_PRICE_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_PRICE_SOURCE),
            ]
        )

        # arrival price's market volume
        frame = frame.with_columns(
            pl.when(buy_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_ASK_SIZE))
            .when(sell_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_BID_SIZE))
            .otherwise(pl.lit(None))
            .alias(PriceColumns.ARRIVAL_PRICE_MARKET_VOLUME)
        )

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        # price/metric percent diff
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.ARRIVAL_PRICE,
            percentage_change_column=PriceColumns.ARRIVAL_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.ARRIVAL_PERCENT_CHANGE,
            output_column=PriceColumns.ARRIVAL_PERFORMANCE,
        )

        # price/metric difference
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.ARRIVAL_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.ARRIVAL_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.ARRIVAL_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.ARRIVAL_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_ask_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """ask price from quotes before trading datetime.

        :param frame:
        :return:
        """
        if QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE not in frame.collect_schema().names():
            return frame
        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        metric_column = PriceColumns.ASK_PRICE
        frame = frame.with_columns(
            [
                # ask price
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(metric_column),
                # ask price's market volume
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.ASK_PRICE_MARKET_VOLUME),
                # execution / ask price's time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.TRADING_DATE_TIME)
                    - pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .cast(pl.Int64)
                .alias(PriceColumns.ASK_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ASK_PRICE_SOURCE),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.ASK_PRICE,
            percentage_change_column=PriceColumns.ASK_PERCENT_CHANGE,
        )

        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.ASK_PERCENT_CHANGE,
            output_column=PriceColumns.ASK_PRICE_PERFORMANCE,
        )

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.ASK_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.ASK_PRICE_DIFFERENCE)
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.ASK_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.ASK_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_bid_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """ask price from quotes before trading datetime.

        :param frame:
        :return:
        """
        if QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE not in frame.collect_schema().names():
            return frame
        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        metric_column = PriceColumns.BID_PRICE
        frame = frame.with_columns(
            [
                # bid price
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(metric_column),
                # bid price's market volume
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.BID_PRICE_MARKET_VOLUME),
                # execution / bid price's time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.TRADING_DATE_TIME)
                    - pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .cast(pl.Int64)
                .alias(PriceColumns.BID_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.BID_PRICE_SOURCE),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.BID_PRICE,
            percentage_change_column=PriceColumns.BID_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.BID_PERCENT_CHANGE,
            output_column=PriceColumns.BID_PRICE_PERFORMANCE,
        )

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.BID_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.BID_PRICE_DIFFERENCE)
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.BID_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.BID_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_mid_point_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """mid price from quotes before trading datetime.

        :param frame:
        :return:
        """
        if QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE not in frame.collect_schema().names():
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        metric_column = PriceColumns.MID_PRICE
        frame = frame.with_columns(
            [
                # mid price
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.MID_PRICE),
                # execution / ask price's time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.TRADING_DATE_TIME)
                    - pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .cast(pl.Int64)
                .alias(PriceColumns.MID_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.MID_PRICE_SOURCE),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        # mid point price % diff vs execution price
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.MID_PRICE,
            percentage_change_column=PriceColumns.MID_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.MID_PERCENT_CHANGE,
            output_column=PriceColumns.MID_PRICE_PERFORMANCE,
        )

        # mid point price diff vs execution price
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.MID_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.MID_PRICE_DIFFERENCE)
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.MID_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.MID_POINT_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_touch(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """nearest quote price from quotes before trading datetime.

        :param frame:
        :return:
        """
        if not set(
            [
                QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
                QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
            ]
        ).issubset(frame.collect_schema().names()):
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])

        metric_column = PriceColumns.TOUCH_PRICE
        frame = frame.with_columns(
            # touch price
            pl.when(buy_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE))
            .when(sell_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.TOUCH_PRICE),
            # execution ts / touch price ts difference
            pl.when(predicate)
            .then(
                pl.col(Alias.TRADING_DATE_TIME)
                - pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME)
            )
            .otherwise(pl.lit(None))
            .cast(pl.Int64)
            .alias(PriceColumns.TOUCH_TIME_DIFFERENCE),
            pl.when(predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
            .otherwise(pl.lit(None))
            .alias(PriceColumns.TOUCH_PRICE_SOURCE),
        )
        # todo: this is in separate with_columns due to unexpected polars behaviour with
        # price metric
        frame = frame.with_columns(  # touch price's market volume
            pl.when(buy_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE))
            .when(sell_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.TOUCH_PRICE_MARKET_VOLUME)
        )

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.TOUCH_PRICE,
            percentage_change_column=PriceColumns.TOUCH_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.TOUCH_PERCENT_CHANGE,
            output_column=PriceColumns.TOUCH_PRICE_PERFORMANCE,
        )

        # price/metric difference
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.TOUCH_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.TOUCH_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.TOUCH_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.TOUCH_DETAILS,
            frame=frame,
        )

        if QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE in frame.collect_schema().names():
            frame = frame.with_columns(
                pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE).alias(
                    PriceColumns.TOUCH_PRICE_SOURCE
                )
            )

        return frame

    def _assign_market_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """Price from trades with execution ts, if empty or trades does not
        exist, mid price from quotes.

        :param frame:
        :return:
        """
        frame = frame.with_columns(
            [
                pl.lit(None).alias(RecordFlags.TCA_STATUS),
                pl.lit(None).cast(pl.Float64).alias(PriceColumns.MARKET_PRICE),
                pl.lit(None).cast(pl.Float64).alias(PriceColumns.MARKET_PRICE_SOURCE),
            ]
        )

        if (
            TradeTickColumnsRenamed.EXECUTION_TS_PRICE not in frame.collect_schema().names()
            and QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE not in frame
        ):
            predicate = pl.col(RecordFlags.TCA_STATUS).is_null()

            frame = frame.with_columns(
                pl.when(predicate)
                .then([TCAFlagStatus.MISSING_MARKET_DATA, TCAFlagStatus.MISS])
                .otherwise(pl.col(RecordFlags.TCA_STATUS))
                .alias(RecordFlags.TCA_STATUS)
            )
            return frame

        metric_column = PriceColumns.MARKET_PRICE
        if TradeTickColumnsRenamed.EXECUTION_TS_PRICE in frame.collect_schema().names():
            predicate = pl.col(PriceAuditing.TRADES_EXECUTED_BEFORE_OPEN).not_()

            frame = frame.with_columns(
                [
                    # market price
                    pl.when(predicate)
                    .then(pl.col(TradeTickColumnsRenamed.EXECUTION_TS_PRICE))
                    .otherwise(pl.col(PriceColumns.MARKET_PRICE))
                    .cast(pl.Float64)
                    .alias(metric_column),
                    pl.when(predicate)
                    .then(pl.col(TradeTickColumnsRenamed.EXECUTION_TS_VENUE))
                    .otherwise(pl.col(PriceColumns.MARKET_PRICE_SOURCE))
                    .alias(PriceColumns.MARKET_PRICE_SOURCE),
                ]
            )

        if QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE in frame.collect_schema().names():
            predicate = (
                pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
                & pl.col(TCAFlagBoolColumns.TRADE_TS_INTEGRITY).not_()
                & pl.col(PriceColumns.MARKET_PRICE).is_null()
            )
            frame = frame.with_columns(
                [
                    pl.when(predicate)
                    .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE))
                    .otherwise(pl.col(metric_column))
                    .cast(pl.Float64)
                    .alias(metric_column),
                    pl.when(predicate)
                    .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                    .otherwise(pl.col(PriceColumns.MARKET_PRICE_SOURCE))
                    .alias(PriceColumns.MARKET_PRICE_SOURCE),
                ]
            )

        frame = self._assign_market_price_flag(frame=frame)
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.MARKET_PRICE,
            percentage_change_column=PriceColumns.MARKET_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.MARKET_PERCENT_CHANGE,
            output_column=PriceColumns.MARKET_PERFORMANCE,
        )

        predicate = pl.col(PriceColumns.MARKET_PRICE).is_not_null()

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.MARKET_PRICE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.MARKET_PRICE_DIFFERENCE)
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.MARKET_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.MARKET_PRICE_DETAILS,
            frame=frame,
        )
        return frame

    def _assign_close_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """
        if EoDStatsColumns.CLOSE_PRICE not in frame.collect_schema().names():
            return frame
        metric_column = PriceColumns.CLOSE_PRICE
        frame = frame.rename({EoDStatsColumns.CLOSE_PRICE: metric_column})
        predicate = pl.col(PriceColumns.CLOSE_PRICE).is_not_null()

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.CLOSE_PRICE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.CLOSE_PRICE_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(EoDStatsColumns.VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.CLOSE_PRICE_SOURCE),
            ]
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.CLOSE_PRICE,
            percentage_change_column=PriceColumns.CLOSE_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.CLOSE_PERCENT_CHANGE,
            output_column=PriceColumns.CLOSE_PRICE_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.CLOSE_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.CLOSE_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_vwap(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """
        if EoDStatsColumns.VWAP not in frame.collect_schema().names():
            return frame.with_columns(pl.lit(None).cast(pl.Float64).alias(PriceColumns.VWAP))

        metric_column = PriceColumns.VWAP

        frame = frame.rename({EoDStatsColumns.VWAP: metric_column})
        frame = frame.with_columns(pl.col(PriceColumns.VWAP).cast(pl.Float64))
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        predicate = pl.col(PriceColumns.VWAP).is_not_null() & pl.col(Alias.PRICE).is_not_null()
        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.VWAP))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.VWAP_PRICE_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(EoDStatsColumns.VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.VWAP_SOURCE),
            ]
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.VWAP,
            percentage_change_column=PriceColumns.VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.VWAP_PERCENT_VS,
            output_column=PriceColumns.VWAP_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.VWAP_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_vwap_limit_adjusted(
        self, frame: pl.LazyFrame, trades: pl.DataFrame, ric_venue: Optional[str], **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """
        metric_column = PriceColumns.LIMIT_ADJUSTED_VWAP

        frame = frame.with_columns(
            pl.struct([Alias.LIMIT_PRICE, Alias.BUY_SELL, PriceColumns.VWAP])
            .map_elements(
                lambda row: (
                    self._calc_limit_adjusted_vwap(row=row, trades=trades)
                    if row.get(Alias.LIMIT_PRICE)
                    else [
                        row.get(PriceColumns.VWAP),
                        row.get(PriceColumns.MARKET_DAY_TRADED_VOLUME),
                    ]
                ),
                return_dtype=pl.List(pl.Float64),
            )
            .alias("result")
        )

        frame = frame.select(
            [
                pl.exclude("result"),
                pl.col("result").list.get(0).cast(pl.Float64).alias(metric_column),
                pl.col("result")
                .list.get(1)
                .cast(pl.Float64)
                .alias(PriceColumns.LIMIT_ADJUSTED_MARKET_VOLUME),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        predicate = pl.col(PriceColumns.LIMIT_ADJUSTED_VWAP).is_not_null()

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.LIMIT_ADJUSTED_VWAP))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.LIMIT_ADJUSTED_VWAP_PRICE_DIFFERENCE)
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.LIMIT_ADJUSTED_VWAP,
            percentage_change_column=PriceColumns.LIMIT_ADJUSTED_VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.LIMIT_ADJUSTED_VWAP_PERCENT_VS,
            output_column=PriceColumns.LIMIT_ADJUSTED_VWAP_PERFORMANCE,
        )
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.LIMIT_ADJUSTED_ALL_DAY_VWAP_DETAILS,
            frame=frame,
        )

        if ric_venue:
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.lit(ric_venue))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.LIMIT_ADJUSTED_VWAP_SOURCE)
            )

        return frame

    def _assign_vwap_start_to_close(
        self, frame: pl.LazyFrame, trades: pl.DataFrame, ric_venue: Optional[str], **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """

        frame = frame.with_columns(
            pl.struct([Alias.ORDER_RECEIVED])
            .map_elements(
                lambda row: self._calc_start_to_close_vwap(row=row, trades=trades),
                return_dtype=pl.List(pl.Float64),
            )
            .alias("result")
        )
        metric_column = PriceColumns.START_TO_CLOSE_VWAP
        frame = frame.select(
            [
                pl.exclude("result"),
                pl.col("result").list.get(0).cast(pl.Float64).alias(metric_column),
                pl.col("result")
                .list.get(1)
                .cast(pl.Float64)
                .alias(PriceColumns.START_TO_CLOSE_MARKET_VOLUME),
            ]
        )
        predicate = pl.col(metric_column).is_not_null()
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(PriceColumns.START_TO_CLOSE_VWAP))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.START_TO_CLOSE_VWAP_PRICE_DIFFERENCE)
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.START_TO_CLOSE_VWAP,
            percentage_change_column=PriceColumns.START_TO_CLOSE_VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.START_TO_CLOSE_VWAP_PERCENT_VS,
            output_column=PriceColumns.START_TO_CLOSE_VWAP_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.START_TO_CLOSE_VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.START_TO_CLOSE_VWAP_DETAILS,
            frame=frame,
        )

        if ric_venue:
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.lit(ric_venue))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.START_TO_CLOSE_VWAP_SOURCE)
            )

        return frame

    def _assign_vwap_start_to_close_limit_adjusted(
        self, frame: pl.LazyFrame, trades: pl.DataFrame, ric_venue: Optional[str], **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """

        frame = frame.with_columns(
            pl.struct(
                [
                    Alias.LIMIT_PRICE,
                    Alias.BUY_SELL,
                    Alias.ORDER_RECEIVED,
                    PriceColumns.START_TO_CLOSE_VWAP,
                    PriceColumns.START_TO_CLOSE_MARKET_VOLUME,
                ]
            )
            .map_elements(
                lambda row: (
                    self._calc_start_to_close_limit_adjusted_vwap(row=row, trades=trades)
                    if row.get(Alias.LIMIT_PRICE)
                    else [
                        row.get(PriceColumns.START_TO_CLOSE_VWAP),
                        row.get(PriceColumns.START_TO_CLOSE_MARKET_VOLUME),
                    ]
                ),
                return_dtype=pl.List(pl.Float64),
            )
            .alias("result")
        )
        metric_column = PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP
        frame = frame.select(
            [
                pl.exclude("result"),
                pl.col("result").list.get(0).cast(pl.Float64).alias(metric_column),
                pl.col("result")
                .list.get(1)
                .cast(pl.Float64)
                .alias(PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_MARKET_VOLUME),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        predicate = pl.col(PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP).is_not_null()

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_PRICE_DIFFERENCE)
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP,
            percentage_change_column=PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_PERCENT_VS,
            output_column=PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_DETAILS,
            frame=frame,
        )

        if ric_venue:
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.lit(ric_venue))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.START_TO_CLOSE_LIMIT_ADJUSTED_VWAP_SOURCE)
            )

        return frame

    def _assign_vwap_submit_to_execution(
        self, frame: pl.LazyFrame, trades: pl.DataFrame, ric_venue: Optional[str], **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """

        frame = frame.with_columns(
            pl.struct([Alias.ORDER_SUBMITTED_EPOCH, Alias.TRADING_DATE_TIME])
            .map_elements(
                lambda row: self._calc_start_execution_vwap(
                    row=row, trades=trades, timestamp_column=Alias.ORDER_SUBMITTED_EPOCH
                ),
                return_dtype=pl.List(pl.Float64),
            )
            .alias("result")
        )
        metric_column = PriceColumns.SUBMIT_TO_EXECUTION_VWAP
        frame = frame.select(
            [
                pl.exclude("result"),
                pl.col("result").list.get(0).cast(pl.Float64).alias(metric_column),
                pl.col("result")
                .list.get(1)
                .cast(pl.Float64)
                .alias(PriceColumns.SUBMIT_TO_EXECUTION_MARKET_VOLUME),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)
        predicate = pl.col(metric_column).is_not_null()

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.SUBMIT_TO_EXECUTION_VWAP_PRICE_DIFFERENCE)
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.SUBMIT_TO_EXECUTION_VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.SUBMIT_TO_EXECUTION_VWAP_PERCENT_VS,
            output_column=PriceColumns.SUBMIT_TO_EXECUTION_VWAP_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.SUBMIT_TO_EXECUTION_VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.SUBMIT_TO_EXECUTION_VWAP_DETAILS,
            frame=frame,
        )

        if ric_venue:
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.lit(ric_venue))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.SUBMIT_TO_EXECUTION_VWAP_SOURCE)
            )

        return frame

    def _assign_vwap_start_to_execution(
        self, frame: pl.LazyFrame, trades: pl.DataFrame, ric_venue: Optional[str], **kwargs
    ) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """

        frame = frame.with_columns(
            pl.struct([Alias.ORDER_RECEIVED, Alias.TRADING_DATE_TIME])
            .map_elements(
                lambda row: self._calc_start_execution_vwap(
                    row=row, trades=trades, timestamp_column=Alias.ORDER_RECEIVED
                ),
                return_dtype=pl.List(pl.Float64),
            )
            .alias("result")
        )
        metric_column = PriceColumns.START_TO_EXECUTION_VWAP
        frame = frame.select(
            [
                pl.exclude("result"),
                pl.col("result").list.get(0).cast(pl.Float64).alias(metric_column),
                pl.col("result")
                .list.get(1)
                .cast(pl.Float64)
                .alias(PriceColumns.START_TO_EXECUTION_MARKET_VOLUME),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)
        predicate = pl.col(metric_column).is_not_null()

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.START_TO_EXECUTION_VWAP_PRICE_DIFFERENCE)
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.START_TO_EXECUTION_VWAP_PERCENT_VS,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.START_TO_EXECUTION_VWAP_PERCENT_VS,
            output_column=PriceColumns.START_TO_EXECUTION_VWAP_PERFORMANCE,
        )
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.START_TO_EXECUTION_VWAP_PERCENT_VS,
            metric_details_column=CostDetailsColumns.START_TO_EXECUTION_VWAP_DETAILS,
            frame=frame,
        )

        if ric_venue:
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.lit(ric_venue))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.START_TO_EXECUTION_VWAP_SOURCE)
            )

        return frame

    def _calc_limit_adjusted_vwap(self, row: dict, trades: pl.DataFrame) -> List[Optional[float]]:
        """

        :param row:
        :param trades:
        :return:
        """
        if not {TradeTickColumns.PRICE, TradeTickColumns.VOLUME}.issubset(
            trades.columns
        ) or not row.get(Alias.LIMIT_PRICE):
            return [None, None]

        price = row[Alias.LIMIT_PRICE]

        if row[Alias.BUY_SELL] == BuySellIndicator.BUYI:
            predicate = pl.col(TradeTickColumns.PRICE) >= price
        elif row[Alias.BUY_SELL] == BuySellIndicator.SELL:
            predicate = pl.col(TradeTickColumns.PRICE) <= price
        else:
            return [None, None]

        df = trades.filter(predicate)
        return self._calc_vwap(df=df)

    def _calc_start_to_close_vwap(self, row: dict, trades: pl.DataFrame) -> List[Optional[float]]:
        """

        :param row:
        :param trades:
        :return:
        """

        start = row[Alias.ORDER_RECEIVED]
        if not start or not {TradeTickColumns.PRICE, TradeTickColumns.VOLUME}.issubset(
            trades.columns
        ):
            return [None, None]

        predicate = pl.col(TradeTickColumns.DATE_TIME) >= start
        df = trades.filter(predicate)

        return self._calc_vwap(df=df)

    def _calc_start_to_close_limit_adjusted_vwap(
        self, row: dict, trades: pl.DataFrame
    ) -> List[Optional[float]]:
        """

        :param row:
        :param trades:
        :return:
        """

        start = row[Alias.ORDER_RECEIVED]
        price = row[Alias.LIMIT_PRICE]

        if not (
            start
            and price
            and {TradeTickColumns.PRICE, TradeTickColumns.VOLUME}.issubset(trades.columns)
        ):
            return [None, None]

        predicate = pl.col(TradeTickColumns.DATE_TIME) >= start

        if row[Alias.BUY_SELL] == BuySellIndicator.BUYI:
            predicate = predicate & (pl.col(TradeTickColumns.PRICE) >= price)
        elif row[Alias.BUY_SELL] == BuySellIndicator.SELL:
            predicate = predicate & (pl.col(TradeTickColumns.PRICE) <= price)
        else:
            return [None, None]

        df = trades.filter(predicate).select(
            [pl.col(TradeTickColumns.PRICE), pl.col(TradeTickColumns.VOLUME)]
        )

        return self._calc_vwap(df=df)

    def _calc_start_execution_vwap(
        self, row: dict, trades: pl.DataFrame, timestamp_column: str
    ) -> List[Optional[float]]:
        """

        :param row:
        :param trades:
        :return:
        """
        start = row[timestamp_column]
        execution = row[Alias.TRADING_DATE_TIME]
        if not (
            start
            and execution
            and {TradeTickColumns.PRICE, TradeTickColumns.VOLUME}.issubset(trades.columns)
        ):
            return [None, None]

        predicate = (pl.col(TradeTickColumns.DATE_TIME) >= start) & (
            pl.col(TradeTickColumns.DATE_TIME) <= execution
        )
        df = trades.filter(predicate).select(
            [pl.col(TradeTickColumns.PRICE), pl.col(TradeTickColumns.VOLUME)]
        )

        return self._calc_vwap(df=df)

    @staticmethod
    def _calc_vwap(df: pl.DataFrame) -> List[Optional[float]]:
        """

        :param df:
        :return:
        """
        volume_x_price_exp = (
            (pl.col(TradeTickColumns.PRICE) * pl.col(TradeTickColumns.VOLUME)).alias("result")
        ).sum()

        price_x_volume_sum = (
            df.select(volume_x_price_exp).drop_nulls().get_column("result").to_list()
        )
        volume_sum = (
            df.select(pl.col(TradeTickColumns.VOLUME).sum())
            .get_column("Volume")
            .drop_nans()
            .to_list()
        )

        if not (price_x_volume_sum and volume_sum) or volume_sum[0] == 0:
            return [None, None]

        vwap = price_x_volume_sum[0] / volume_sum[0]
        return [float(vwap), float(volume_sum[0])]

    def _assign_open_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """
        if EoDStatsColumns.OPEN_PRICE not in frame.collect_schema().names():
            return frame
        metric_column = PriceColumns.OPEN_PRICE
        frame = frame.rename({EoDStatsColumns.OPEN_PRICE: metric_column})
        predicate = pl.col(PriceColumns.OPEN_PRICE).is_not_null()
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)
        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.PRICE) - pl.col(metric_column))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.OPEN_PRICE_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(EoDStatsColumns.VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.OPEN_PRICE_SOURCE),
            ]
        )
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.OPEN_PRICE,
            percentage_change_column=PriceColumns.OPEN_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.OPEN_PERCENT_CHANGE,
            output_column=PriceColumns.OPEN_PRICE_PERFORMANCE,
        )

        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.OPEN_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.OPEN_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_mid_arrival_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if QuoteTickColumnsRenamed.EXECUTION_TS_MID_PRICE not in frame.collect_schema().names():
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN).not_()
        metric_column = PriceColumns.ARRIVAL_PRICE_MID
        frame = frame.with_columns(
            [
                # arrival price
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_MID_PRICE))
                .otherwise(pl.lit(None))
                .alias(metric_column),
                # arrival price's market volume
                pl.when(predicate)
                .then(
                    (
                        pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_ASK_SIZE)
                        + pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_BID_SIZE)
                    )
                    / 2
                )
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_MID_PRICE_MARKET_VOLUME),
                # order/arrival time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.ORDER_RECEIVED)
                    - pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_MID_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_RECEIVED_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_MID_SOURCE),
            ]
        )

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        # price/metric percent diff
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.ARRIVAL_MID_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.ARRIVAL_MID_PERCENT_CHANGE,
            output_column=PriceColumns.ARRIVAL_MID_PERFORMANCE,
        )
        # price/metric difference

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.ARRIVAL_MID_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.ARRIVAL_MID_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.ARRIVAL_MID_PRICE_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_first_fill_arrival_price(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if not all(
            c in frame.collect_schema().names()
            for c in [
                QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
                QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
            ]
        ):
            return frame
        metric_column = PriceColumns.ARRIVAL_FIRST_FILL_PRICE
        min_timestamps = (
            frame.group_by(ElasticMetaColumns.AMPERSAND_PARENT)
            .agg(
                [
                    pl.col(
                        [
                            QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
                            QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
                            QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE,
                            QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE,
                            QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME,
                        ]
                    )
                    .sort_by(Alias.TRADING_DATE_TIME)
                    .first(),
                ]
            )
            .rename(
                {
                    QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE: "firstFillAskPrice",
                    QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE: "firstFillBidPrice",
                    QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE: "firstFillBidSize",
                    QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE: "firstFillAskSize",
                    QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME: "firstFillDateTime",
                }
            )
        )
        frame = frame.join(
            min_timestamps,
            left_on=ElasticMetaColumns.AMPERSAND_PARENT,
            right_on=ElasticMetaColumns.AMPERSAND_PARENT,
            how="left",
        )

        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])
        frame = frame.with_columns(
            [
                # arrival price
                pl.when(buy_predicate)
                .then(pl.col("firstFillAskPrice"))
                .when(sell_predicate)
                .then(pl.col("firstFillBidPrice"))
                .otherwise(pl.lit(None))
                .alias(metric_column),
                # order/arrival time difference
                pl.when(predicate)
                .then(pl.col(Alias.TRADING_DATE_TIME) - pl.col("firstFillDateTime"))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_FIRST_FILL_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_FIRST_FILL_PRICE_SOURCE),
            ]
        )
        frame = frame.with_columns(
            pl.when(buy_predicate)
            .then(pl.col("firstFillAskSize"))
            .when(sell_predicate)
            .then(pl.col("firstFillBidSize"))
            .otherwise(pl.lit(None))
            .alias(PriceColumns.ARRIVAL_FIRST_FILL_MARKET_VOLUME)
        )
        frame = frame.drop(
            [
                "firstFillAskPrice",
                "firstFillBidPrice",
                "firstFillBidSize",
                "firstFillAskSize",
                "firstFillDateTime",
            ]
        )
        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        # price/metric percent diff
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.ARRIVAL_FIRST_FILL_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.ARRIVAL_FIRST_FILL_PERCENT_CHANGE,
            output_column=PriceColumns.ARRIVAL_FIRST_FILL_PERFORMANCE,
        )

        # price/metric difference
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.ARRIVAL_FIRST_FILL_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.ARRIVAL_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.ARRIVAL_FIRST_FILL_DETAILS,
            frame=frame,
        )

        return frame

    def _assign_market_submission_arrival(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if (
            Alias.PARENT_ORDER_SUBMITTED not in frame.collect_schema().names()
            or QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_ASK_PRICE
            not in frame.collect_schema().names()
        ):
            return frame

        # TODO: Executed before open for order submitted
        predicate = (
            pl.col(PriceAuditing.QUOTES_ORDER_RECEIVED_EXECUTED_BEFORE_OPEN).not_()
            & pl.col(Alias.BUY_SELL).is_not_null()
        )
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])

        metric_column = PriceColumns.ARRIVAL_MARKET_SUBMISSION_PRICE
        frame = frame.with_columns(
            [
                # arrival price
                pl.when(buy_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_ASK_PRICE))
                .when(sell_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_BID_PRICE))
                .otherwise(pl.lit(None))
                .alias(metric_column),
                # order/arrival time difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.PARENT_ORDER_SUBMITTED)
                    - pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_MARKET_SUBMISSION_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.ARRIVAL_MARKET_SUBMISSION_PRICE_SOURCE),
            ]
        )
        frame = frame.with_columns(
            pl.when(buy_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_ASK_SIZE))
            .when(sell_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.ORDER_SUBMITTED_BID_SIZE))
            .otherwise(pl.lit(None))
            .alias(PriceColumns.ARRIVAL_MARKET_SUBMISSION_PRICE_MARKET_VOLUME)
        )

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        # price/metric percent diff
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.ARRIVAL_MARKET_SUBMISSION_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.ARRIVAL_MARKET_SUBMISSION_PERCENT_CHANGE,
            output_column=PriceColumns.ARRIVAL_MARKET_SUBMISSION_PERFORMANCE,
        )
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.ARRIVAL_MARKET_SUBMISSION_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.ARRIVAL_MARKET_SUBMISSION_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.ARRIVAL_MARKET_SUBMISSION_DETAILS,
            frame=frame,
        )
        return frame

    @staticmethod
    def _assign_market_day_traded_volume(frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if EoDStatsColumns.TRADE_VOLUME not in frame.collect_schema().names():
            return frame.with_columns(pl.lit(None).alias(PriceColumns.MARKET_DAY_TRADED_VOLUME))

        frame = frame.with_columns(
            pl.col(EoDStatsColumns.TRADE_VOLUME).alias(PriceColumns.MARKET_DAY_TRADED_VOLUME)
        )
        predicate = pl.col(PriceColumns.MARKET_DAY_TRADED_VOLUME).is_not_null() & pl.col(
            PriceColumns.MARKET_DAY_TRADED_VOLUME
        ).ne(0)

        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.QUANTITY) / pl.col(PriceColumns.MARKET_DAY_TRADED_VOLUME))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.MARKET_DAY_TRADED_VOLUME_PERCENT_VS),
                pl.when(predicate)
                .then(pl.col(EoDStatsColumns.VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.MARKET_DAY_TRADED_VOLUME_SOURCE),
            ]
        )

        return frame

    def _assign_near_touch(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """nearest quote price from quotes before trading datetime.

        :param frame:
        :return:
        """
        if not set(
            [
                QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE,
                QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE,
            ]
        ).issubset(frame.collect_schema().names()):
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])

        metric_column = PriceColumns.NEAR_TOUCH_PRICE
        frame = frame.with_columns(
            [
                # touch price
                pl.when(buy_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_PRICE))
                .when(sell_predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_PRICE))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(metric_column),
                # execution ts / touch price ts difference
                pl.when(predicate)
                .then(
                    pl.col(Alias.TRADING_DATE_TIME)
                    - pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_QUOTES_DATE_TIME)
                )
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.NEAR_TOUCH_TIME_DIFFERENCE),
                pl.when(predicate)
                .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_RIC_VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.NEAR_TOUCH_PRICE_SOURCE),
            ]
        )

        frame = frame.with_columns(
            pl.when(buy_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_BID_SIZE))
            .when(sell_predicate)
            .then(pl.col(QuoteTickColumnsRenamed.EXECUTION_TS_ASK_SIZE))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.NEAR_TOUCH_PRICE_MARKET_VOLUME)
        )

        frame = self._convert_metric_ccy(metric_column=metric_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=metric_column,
            percentage_change_column=PriceColumns.NEAR_TOUCH_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.NEAR_TOUCH_PERCENT_CHANGE,
            output_column=PriceColumns.NEAR_TOUCH_PRICE_PERFORMANCE,
        )

        # price/metric difference
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(metric_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(PriceColumns.NEAR_TOUCH_PRICE_DIFFERENCE)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.NEAR_TOUCH_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.NEAR_TOUCH_DETAILS,
            frame=frame,
        )

        return frame

    def _limit_vs_execution(self, frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :return:
        """
        # Do not rename the column as limit price is needed further downstream
        frame = frame.with_columns(pl.col(Alias.LIMIT_PRICE).alias(PriceColumns.LIMIT_VS_EXECUTION))
        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=PriceColumns.LIMIT_VS_EXECUTION,
            percentage_change_column=PriceColumns.LIMIT_VS_EXECUTION_PERCENT_CHANGE,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame,
            percent_column=PriceColumns.LIMIT_VS_EXECUTION_PERCENT_CHANGE,
            output_column=PriceColumns.LIMIT_VS_EXECUTION_PERFORMANCE,
        )
        predicate = (
            pl.col(PriceColumns.LIMIT_VS_EXECUTION).is_not_null()
            & pl.col(Alias.PRICE).is_not_null()
        )

        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(PriceColumns.LIMIT_VS_EXECUTION) - pl.col(Alias.PRICE))
            .otherwise(pl.lit(None))
            .alias(PriceColumns.LIMIT_VS_EXECUTION_PRICE_DIFFERENCE),
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=PriceColumns.LIMIT_VS_EXECUTION_PERCENT_CHANGE,
            metric_details_column=CostDetailsColumns.LIMIT_VS_EXECUTION_DETAILS,
            frame=frame,
        )

        return frame

    @staticmethod
    def _assign_market_avg_daily_volume(frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if EoDStatsColumns.VOLUME_EMA not in frame.collect_schema().names():
            return frame

        frame = frame.with_columns(
            pl.col(EoDStatsColumns.VOLUME_EMA).alias(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME)
        )
        predicate = (
            pl.col(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME).is_not_null()
            & pl.col(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME).is_in([0]).not_()
        )

        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.QUANTITY) / pl.col(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME_PERCENT_VS),
                pl.when(predicate)
                .then(pl.col(EoDStatsColumns.VENUE))
                .otherwise(pl.lit(None))
                .alias(PriceColumns.MARKET_AVG_DAILY_TRADED_VOLUME_SOURCE),
            ]
        )

        return frame

    @staticmethod
    def _assign_volume_volatility(frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if EoDStatsColumns.VOLUME_VOLATILITY not in frame.collect_schema().names():
            return frame

        frame = frame.rename({EoDStatsColumns.VOLUME_VOLATILITY: PriceColumns.VOLUME_VOLATILITY})

        return frame

    @staticmethod
    def _assign_price_volatility(frame: pl.LazyFrame, **kwargs) -> pl.LazyFrame:
        """

        :param frame:
        :param kwargs:
        :return:
        """
        if EoDStatsColumns.PRICE_VOLATILITY not in frame.collect_schema().names():
            return frame

        frame = frame.rename({EoDStatsColumns.PRICE_VOLATILITY: PriceColumns.PRICE_VOLATILITY})
        return frame

    def _assign_preval_quotes(
        self,
        frame: pl.LazyFrame,
        price_column: str,
        percent_change_column: str,
        performance_column: str,
        price_diff_column: str,
        details_column: str,
        ask_price_column: str,
        bid_price_column: str,
        source_column: str,
        **kwargs,
    ) -> pl.LazyFrame:
        """

        :param frame:
        :param price_column:
        :param percent_change_column:
        :param price_diff_column:
        :param details_column:
        :param ask_price_column:
        :param bid_price_column:
        :param kwargs:
        :return:
        """

        if not set([ask_price_column, bid_price_column]).issubset(frame.collect_schema().names()):
            return frame

        predicate = pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN).not_()
        buy_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.BUYI.value])
        sell_predicate = predicate & pl.col(Alias.BUY_SELL).is_in([BuySellIndicator.SELL.value])

        frame = frame.with_columns(
            [
                # touch price
                pl.when(buy_predicate)
                .then(pl.col(ask_price_column))
                .when(sell_predicate)
                .then(pl.col(bid_price_column))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(price_column),
                pl.when(predicate)
                .then(pl.col(source_column))
                .otherwise(pl.lit(None))
                .alias(details_column + ".source"),
            ]
        )
        frame = self._convert_metric_ccy(metric_column=price_column, df=frame)

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=price_column,
            percentage_change_column=percent_change_column,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame, percent_column=percent_change_column, output_column=performance_column
        )
        # price/metric difference
        frame = frame.with_columns(
            pl.when(predicate)
            .then(pl.col(Alias.PRICE) - pl.col(price_column))
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(price_diff_column)
        )

        # total cost difference in other currencies
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=percent_change_column,
            metric_details_column=details_column,
            frame=frame,
        )

        return frame

    def _assign_preval_stats(
        self,
        frame: pl.LazyFrame,
        price_column: str,
        percent_change_column: str,
        performance_column: str,
        price_diff_column: str,
        details_column: str,
        close_price_column: str,
        source_column: str,
    ) -> pl.LazyFrame:
        """

        :param frame:
        :param price_column:
        :param percent_change_column:
        :param price_diff_column:
        :param details_column:
        :param close_price_column:
        :return:
        """
        if close_price_column not in frame.collect_schema().names():
            return frame

        frame = frame.rename({close_price_column: price_column})
        frame = self._convert_metric_ccy(metric_column=price_column, df=frame)

        predicate = pl.col(price_column).is_not_null()
        frame = frame.with_columns(
            [
                pl.when(predicate)
                .then(pl.col(Alias.PRICE) - pl.col(price_column))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(price_diff_column),
                pl.when(predicate)
                .then(pl.col(source_column))
                .otherwise(pl.lit(None))
                .alias(details_column + ".source"),
            ]
        )

        frame = self._metric_percent_vectorized(
            frame=frame,
            metric_column=price_column,
            percentage_change_column=percent_change_column,
        )
        frame = self._assign_side_adjusted_performance(
            frame=frame, percent_column=percent_change_column, output_column=performance_column
        )
        frame = self._cost_vs_percentage_benchmark(
            percentage_change_column=percent_change_column,
            metric_details_column=details_column,
            frame=frame,
        )

        return frame

    @staticmethod
    def _metric_percent_vectorized(
        frame: pl.LazyFrame,
        metric_column: str,
        percentage_change_column: str,
        executed_price_column: str = Alias.PRICE,
    ) -> pl.LazyFrame:
        """Calculate metric percent change of given execution price and the
        metric we compare against.

        :param frame:  our order price
        :param executed_price_column:  our order price
        :param metric_column: price we compare against
        :param percentage_change_column: price we compare against

        :return: percentage change
        """

        predicate = (
            ((pl.col(executed_price_column) + pl.col(metric_column)) != 0)
            & pl.col(metric_column).is_not_null()
            & pl.col(executed_price_column).is_not_null()
        )
        return frame.with_columns(
            pl.when(predicate)
            .then(
                (pl.col(executed_price_column) - pl.col(metric_column))
                / ((pl.col(executed_price_column) + pl.col(metric_column)) / 2)
            )
            .otherwise(pl.lit(None))
            .cast(pl.Float64)
            .alias(percentage_change_column)
        )

    @staticmethod
    def _assign_side_adjusted_performance(
        frame: pl.LazyFrame,
        percent_column: str,
        output_column: str,
        buy_sell_column: str = Alias.BUY_SELL,
    ) -> pl.LazyFrame:
        return frame.with_columns(
            pl.when(pl.col(buy_sell_column) == BuySellIndicator.BUYI.value)
            .then(-pl.col(percent_column))
            .when(pl.col(buy_sell_column) == BuySellIndicator.SELL.value)
            .then(pl.col(percent_column))
            .otherwise(None)
            .alias(output_column)
        )

    @staticmethod
    def _assign_market_price_flag(frame: pl.LazyFrame) -> pl.LazyFrame:
        """

        :param frame:
        :return:
        """
        data_integrity_predicate = pl.col(PriceColumns.MARKET_PRICE).is_null() & pl.col(
            TCAFlagBoolColumns.TRADE_TS_INTEGRITY
        )
        execution_before_market_predicate = (
            pl.col(PriceColumns.MARKET_PRICE).is_null()
            & pl.col(RecordFlags.TCA_STATUS).is_null()
            & (
                pl.col(PriceAuditing.TRADES_EXECUTED_BEFORE_OPEN)
                | pl.col(PriceAuditing.QUOTES_EXECUTION_TS_EXECUTED_BEFORE_OPEN)
            )
        )

        missing_market_data_predicate = (
            pl.col(PriceColumns.MARKET_PRICE).is_null() & pl.col(RecordFlags.TCA_STATUS).is_null()
        )

        hit_predicate = pl.col(PriceColumns.MARKET_PRICE).is_not_null()
        converted_ccy_predicate = pl.col(Alias.CURRENCY_TRANSFORM) & hit_predicate

        frame = frame.with_columns(
            pl.when(data_integrity_predicate)
            .then([TCAFlagStatus.DATA_INTEGRITY, TCAFlagStatus.MISS])
            .otherwise(pl.lit(None))
            .alias(RecordFlags.TCA_STATUS)
        )

        frame = frame.with_columns(
            pl.when(execution_before_market_predicate)
            .then([TCAFlagStatus.EXECUTION_BEFORE_MARKET, TCAFlagStatus.MISS])
            .otherwise(pl.col(RecordFlags.TCA_STATUS))
            .alias(RecordFlags.TCA_STATUS)
        )
        frame = frame.with_columns(
            pl.when(missing_market_data_predicate)
            .then([TCAFlagStatus.MISSING_MARKET_DATA, TCAFlagStatus.MISS])
            .otherwise(pl.col(RecordFlags.TCA_STATUS))
            .alias(RecordFlags.TCA_STATUS)
        )

        frame = frame.with_columns(
            pl.when(hit_predicate)
            .then([TCAFlagStatus.HIT])
            .otherwise(pl.col(RecordFlags.TCA_STATUS))
            .alias(RecordFlags.TCA_STATUS)
        )
        frame = frame.with_columns(
            pl.when(converted_ccy_predicate)
            .then([TCAFlagStatus.HIT, TCAFlagStatus.CONVERTED_CURRENCY])
            .otherwise(pl.col(RecordFlags.TCA_STATUS))
            .alias(RecordFlags.TCA_STATUS)
        )

        return frame

    @staticmethod
    def _cost_vs_percentage_benchmark(
        percentage_change_column: str, metric_details_column: str, frame: pl.LazyFrame
    ) -> pl.LazyFrame:
        """transactionDetails.pricingDetails.percentVs<Benchmark> *
        bestExecutionData.transactionVolume.ecbRefRate.<CUR> for all target
        currencies; "CHF", "GBP", "EUR", "JPY", "USD".

        :param percentage_change_column:
        :param frame:
        :return:
        """
        ref_rates = [
            ccy
            for ccy in [
                (Alias.ECB_REF_RATES_EUR, "EUR"),
                (Alias.ECB_REF_RATES_JPY, "JPY"),
                (Alias.ECB_REF_RATES_CHF, "CHF"),
                (Alias.ECB_REF_RATES_USD, "USD"),
                (Alias.ECB_REF_RATES_GBP, "GBP"),
                (Alias.ECB_REF_RATES_NATIVE_CCY_VOLUME, "native"),
            ]
            if ccy[0] in frame.collect_schema().names()
        ]

        if not (percentage_change_column in frame.collect_schema().names() and ref_rates):
            return frame

        for ccy_col, ccy in ref_rates:
            column = metric_details_column + ".cost." + ccy
            predicate = (
                pl.col(percentage_change_column).is_not_null() & pl.col(ccy_col).is_not_null()
            )
            frame = frame.with_columns(
                (
                    pl.when(predicate)
                    .then(pl.col(percentage_change_column) * pl.col(ccy_col))
                    .otherwise(pl.lit(None))
                    .cast(pl.Float64)
                    .alias(column)
                )
            )

        if metric_details_column + ".amount.native" in frame.collect_schema().names():
            predicate = (
                pl.col(percentage_change_column).is_not_null()
                & pl.col(metric_details_column + ".amount.native").is_not_null()
            )
            frame = frame.with_columns(
                pl.when(predicate)
                .then(pl.col(Alias.ECB_REF_RATES_NATIVE_CCY))
                .otherwise(pl.lit(None))
                .cast(pl.Float64)
                .alias(metric_details_column + ".amount.nativeCurrency")
            )

        return frame

    @staticmethod
    def _convert_metric_ccy(metric_column: str, df: pl.LazyFrame) -> pl.LazyFrame:
        """

        :param metric_column:
        :param df:
        :return:
        """
        return df.with_columns(
            pl.when(pl.col(Alias.CURRENCY_TRANSFORM))
            .then(pl.col(metric_column) * pl.col(Alias.FX_CONVERSION_RATE))
            .otherwise(pl.col(metric_column))
            .alias(metric_column)
        )
