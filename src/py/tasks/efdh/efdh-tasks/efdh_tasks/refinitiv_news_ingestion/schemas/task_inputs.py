from efdh_tasks.refinitiv_news_ingestion.schemas.sqs_messsage import SQSMessage
from pydantic import BaseModel
from typing import Any, Dict, List


class DecryptionTaskInput(BaseModel):
    """Custom wrapper of Pydantic Base Model for Lexica Processor Aries
    Task."""

    message: SQSMessage


class Meta(BaseModel):
    subjects: List[Dict[str, Any]]
    keywords: List[Dict[str, Any]]
    qcodes: List[Dict[str, Any]]
    stories: List[Dict[str, Any]]


class PostgresUploadTaskInput(BaseModel):
    """Custom wrapper of Pydantic Base Model for S3 Upload Task."""

    meta: Meta
    content: Dict[str, Any]


class S3UploadTaskInput(BaseModel):
    """Custom wrapper of Pydantic Base Model for S3 Upload Task."""

    meta: Meta
    content: Dict[str, Any]


class NormalisedMessage(BaseModel):
    """Custom wrapper of Pydantic Base Model for S3 Upload Task."""

    meta: Meta
    content: Dict[str, Any]
