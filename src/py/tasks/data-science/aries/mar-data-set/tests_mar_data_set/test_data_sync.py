import pytest
from mar_data_set.data_sync import ParquetDataSync, ParquetDataSyncConfig
from unittest import mock
from unittest.mock import MagicMock, patch


@pytest.fixture
def config():
    return ParquetDataSyncConfig(
        DATA_SYNC_SOURCE_ALERTS_PATH="s3://source-bucket/alerts.parquet",
        DATA_SYNC_SOURCE_SCENARIOS_PATH="s3://source-bucket/scenarios.parquet",
        DATA_SYNC_DESTINATION_BUCKET="s3://dest-bucket",
        DATA_SYNC_MAPPING_DESTINATION_BUCKET="s3://mapping-bucket",
    )


@patch("mar_data_set.data_sync.uuid.uuid4")
@patch("mar_data_set.data_sync.fsspec.filesystem")
def test_generate_random_filename(mock_fs, mock_uuid, config):
    mock_uuid.return_value.hex = "abc123"
    syncer = ParquetDataSync(tenant="test-tenant", params=config)
    filename = syncer.generate_random_filename("prefix")
    assert filename == "prefix_abc123.parquet"


@patch("mar_data_set.data_sync.uuid.uuid4")
@patch("mar_data_set.data_sync.fsspec.filesystem")
def test_run_success(mock_fs, mock_uuid, config):
    # Mocks
    mock_uuid.side_effect = [
        mock.Mock(hex="alertuuid"),
        mock.Mock(hex="scenuuid"),
        mock.Mock(hex="mappinguuid"),
    ]
    fs_mock = MagicMock()
    mock_fs.return_value = fs_mock

    syncer = ParquetDataSync(tenant="test-tenant", params=config)

    # Mock copy_file
    syncer.copy_file = MagicMock()

    # Mock fs.open to return a context manager
    mock_file = mock.mock_open()
    fs_mock.open = mock_file

    mapping = syncer.run()

    alerts_filename = "tsurv_data_set/alerts_alertuuid.parquet"
    scenarios_filename = "tsurv_data_set/scenarios_scenuuid.parquet"

    dest_alerts_path = f"s3://dest-bucket/{alerts_filename}"
    dest_scenarios_path = f"s3://dest-bucket/{scenarios_filename}"

    # Assert copy_file called correctly
    syncer.copy_file.assert_any_call(config.DATA_SYNC_SOURCE_ALERTS_PATH, dest_alerts_path)
    syncer.copy_file.assert_any_call(config.DATA_SYNC_SOURCE_SCENARIOS_PATH, dest_scenarios_path)

    # Assert mapping file was written
    mock_file().write.assert_called()  # Basic check that write was called

    # Assert returned mapping is correct
    assert mapping["tenant_name"] == "test-tenant"
    assert mapping["alerts_path"] == dest_alerts_path
    assert mapping["scenarios_path"] == dest_scenarios_path
