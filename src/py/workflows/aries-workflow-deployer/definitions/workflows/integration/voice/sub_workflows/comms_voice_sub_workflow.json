{"inputParameters": [], "inputTemplate": {"workflow": {"name": "comms_voice_sub_workflow"}}, "name": "comms_voice_sub_workflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta", "type": "WAIT"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"DeepGram": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${wait_es__apply_meta.output.io_param}", "task": "${wait_es__apply_meta.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_deepgram_api", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "deepgram_api", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"empty_file_uri": [{"asyncComplete": false, "inputParameters": {"duration": "0 seconds"}, "name": "skip_empty", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "skip_deepgram", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_api.output.io_param}", "task": "${deepgram_api.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_deepgram_feed", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "deepgram_feed", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"with-analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_feed.output.io_param.params.Call}", "task": "${deepgram_feed.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "lexica_processor", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lexica_processor", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"with-transcription-copilot": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": {"params": {"Call": "${lexica_processor.output.io_param}", "Transcript": "${deepgram_feed.output.io_param.params.Transcript}", "tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}}, "task": "${lexica_processor.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_transcription_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "copilot_transcription_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${copilot_transcription_analytics.output.io_param.params.Transcript}", "task": "${copilot_transcription_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_transcript_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_transcript_with_analytics_and_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_transcript_with_analytics_and_copilot", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${copilot_transcription_analytics.output.io_param.params.Call}", "task": "${copilot_transcription_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_update_calls_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_update_calls_with_analytics_and_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_update_calls_with_analytics_and_copilot", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_with_analytics_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_with_analytics_and_copilot", "wait_es__apply_meta_update_calls_with_analytics_and_copilot"], "loopCondition": null, "name": "calls_transcripts_with_analytics_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_join", "type": "JOIN"}], "without-transcription-copilot": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_feed.output.io_param.params.Transcript}", "task": "${deepgram_feed.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_transcript_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_transcript_with_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_transcript_with_analytics", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${lexica_processor.output.io_param}", "task": "${lexica_processor.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_update_calls_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_update_calls_with_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_update_calls_with_analytics", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_with_analytics_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_with_analytics", "wait_es__apply_meta_update_calls_with_analytics"], "loopCondition": null, "name": "calls_transcripts_with_analytics_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_join", "type": "JOIN"}]}, "description": "Decide whether to run Transcription Co-Pilot for DeepGram based Transcript on the analytics_controller", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () { if ($.tenant_config_feature_flags.indexOf('azure-processing') > -1) { return 'with-transcription-copilot'; } else { return 'without-transcription-copilot'; } })();", "inputParameters": {"tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}, "loopCondition": null, "name": "switch_transcription_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_transcription_copilot", "type": "SWITCH"}], "without-analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_feed.output.io_param.params.Transcript}", "task": "${deepgram_feed.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_transcript_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_transcript_without_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_transcript_without_analytics", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_feed.output.io_param.params.Call}", "task": "${deepgram_feed.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_update_calls_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_update_calls_without_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_update_calls_without_analytics", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_without_analytics_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_without_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_without_analytics", "wait_es__apply_meta_update_calls_without_analytics"], "loopCondition": null, "name": "calls_transcripts_without_analytics_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_without_analytics_join", "type": "JOIN"}]}, "defaultCase": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${deepgram_feed.output.io_param.params.Call}", "task": "${deepgram_feed.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_update_calls_no_transcripts", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_update_calls_no_transcripts", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_update_calls_no_transcripts", "type": "WAIT"}], "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () { if ($.hasTranscriptions && $.is_lexica_enabled === true) { return 'with-analytics'; } else if ($.hasTranscriptions && $.is_lexica_enabled === false) { return 'without-analytics'; } })();", "inputParameters": {"hasTranscriptions": "${deepgram_feed.output.io_param.params.Transcript}", "is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "loopCondition": null, "name": "switch_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_analytics", "type": "SWITCH"}], "description": "Decide if there is an NDJSON to process", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri) {\n    return \"process\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();\n", "inputParameters": {"file_uri": "${deepgram_api.output.io_param.params.file_uri}"}, "loopCondition": null, "name": "switch_empty_file_uri", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_empty_file_uri", "type": "SWITCH"}], "IVDirect": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${wait_es__apply_meta.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_iv_transform_transcription", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "aries_iv_transform_transcription", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"with-analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param.params.Call}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "lexica_processor", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lexica_processor_iv_direct", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"with-transcription-copilot": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": {"params": {"Call": "${lexica_processor_iv_direct.output.io_param}", "Transcript": "${aries_iv_transform_transcription.output.io_param.params.Transcript}", "tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}}, "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_transcription_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "aries_transcription_copilot_iv_direct_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_transcription_copilot_iv_direct_with_analytics.output.io_param.params.Transcript}", "task": "${aries_transcription_copilot_iv_direct_with_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_iv_direct_transcript_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_iv_direct_transcript_with_analytics_and_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_iv_direct_transcript_with_analytics_and_copilot", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_transcription_copilot_iv_direct_with_analytics.output.io_param.params.Call}", "task": "${wait_es__apply_meta.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_call_update_iv_direct_with_analytics_and_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_call_update_iv_direct_with_analytics_and_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_call_update_iv_direct_with_analytics_and_copilot", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_with_analytics_iv_direct_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_iv_direct_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_call_update_iv_direct_with_analytics_and_copilot", "wait_es__apply_meta_iv_direct_transcript_with_analytics_and_copilot"], "loopCondition": null, "name": "calls_transcripts_with_analytics_iv_direct_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_and_copilot_iv_direct_join", "type": "JOIN"}], "without-transcription-copilot": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param.params.Transcript}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_iv_direct_transcript_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_iv_direct_transcript_with_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_iv_direct_transcript_with_analytics", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${lexica_processor_iv_direct.output.io_param}", "task": "${lexica_processor_iv_direct.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_call_update_iv_direct_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_call_update_iv_direct_with_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_call_update_iv_direct_with_analytics", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_with_analytics_iv_direct_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_iv_direct_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_iv_direct_transcript_with_analytics", "wait_es__apply_meta_iv_direct_transcript_with_analytics"], "loopCondition": null, "name": "calls_transcripts_with_analytics_iv_direct_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_with_analytics_iv_direct_join", "type": "JOIN"}]}, "description": "Decide whether to run Transcription Co-Pilot for IV Direct based on the analytics_controller", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () { if ($.tenant_config_feature_flags.indexOf('azure-processing') > -1) { return 'with-transcription-copilot'; } else { return 'without-transcription-copilot'; } })();", "inputParameters": {"tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}, "loopCondition": null, "name": "switch_iv_transcription_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_iv_transcription_copilot", "type": "SWITCH"}], "without-analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": {"params": {"Call": "${aries_iv_transform_transcription.output.io_param.params.Call}", "Transcript": "${aries_iv_transform_transcription.output.io_param.params.Transcript}", "tenant_config_feature_flags": "${workflow.input.io_param.params.tenant_config_feature_flags}"}}, "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_transcription_copilot", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "aries_transcription_copilot_iv_direct_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_transcription_copilot_iv_direct_without_analytics.output.io_param.params.Transcript}", "task": "${aries_transcription_copilot_iv_direct_without_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_transcript_iv_direct_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_transcript_iv_direct_without_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_transcript_iv_direct_without_analytics", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${aries_transcription_copilot_iv_direct_without_analytics.output.io_param.params.Call}", "task": "${wait_es__apply_meta.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_call_update_iv_direct_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "loopCondition": null, "name": "wait_es__apply_meta_call_update_iv_direct_without_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "wait_es__apply_meta_call_update_iv_direct_without_analytics", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "calls_transcripts_without_analytics_iv_direct_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_without_analytics_iv_direct_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["wait_es__apply_meta_transcript_iv_direct_without_analytics", "wait_es__apply_meta_call_update_iv_direct_without_analytics"], "loopCondition": null, "name": "calls_transcripts_without_analytics_iv_direct_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "calls_transcripts_without_analytics_iv_direct_join", "type": "JOIN"}]}, "description": "Decide whether to run analytics for IV Direct based on the analytics_controller", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () { if ($.is_lexica_enabled === true && $.hasCallUpdates && $.hasTranscriptions) { return 'with-analytics'; } else if ($.hasCallUpdates && $.hasTranscriptions) { return 'without-analytics'; } })();", "inputParameters": {"hasCallUpdates": "${aries_iv_transform_transcription.output.io_param.params.Call}", "hasTranscriptions": "${aries_iv_transform_transcription.output.io_param.params.Transcript}", "is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "loopCondition": null, "name": "switch_iv_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_iv_analytics", "type": "SWITCH"}], "IntelligentVoice": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${wait_es__apply_meta.output.io_param}", "task": "${wait_es__apply_meta.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "aries_iv_submit_transcription", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "iv_submit_transcription", "type": "SIMPLE"}]}, "description": "Decide whether to run Deepgram/IV based on the tenant config", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "value-param", "expression": "transcriptionProvider", "inputParameters": {"transcriptionProvider": "${workflow.input.io_param.params.transcription_provider}"}, "loopCondition": null, "name": "switch_transcription_provider", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_transcription_provider", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}