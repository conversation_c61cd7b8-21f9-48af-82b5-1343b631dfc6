import json
import pytest
from batching.agents.fix_agent import stream_by_tenant_fix_bundle_topic_reader
from batching.agents.tw_agent import stream_by_workflow_topic_reader
from collections import OrderedDict
from mock import patch
from mock.mock import MagicMock


@pytest.fixture()
def test_configuration():
    return MagicMock(app=MagicMock())


@pytest.mark.asyncio()
async def test_ping_triggers_tw_batching(test_app, test_configuration, mock_batcher):
    # start and stop the agent in this block
    with patch.multiple(
        "batching.agents.tw_agent",
        configuration=test_configuration,
        tw_batching_service=mock_batcher,
    ) as _:
        async with stream_by_workflow_topic_reader.test_context() as agent:
            key = json.dumps(
                OrderedDict(tenant="tenant", workflow="workflow"),
                separators=(",", ":"),
            )
            await agent.put(key=key.encode("utf8"), value=None)
            mock_batcher.check_batches.assert_called_once()


@pytest.mark.asyncio()
async def test_ping_triggers_fix_batching(test_app, test_configuration, mock_batcher):
    # start and stop the agent in this block
    with patch.multiple(
        "batching.agents.fix_agent",
        configuration=test_configuration,
        fix_batching_service=mock_batcher,
    ) as _:
        async with stream_by_tenant_fix_bundle_topic_reader.test_context() as agent:
            key = json.dumps(
                OrderedDict(tenant="tenant", bundle_id="bundle_id"),
                separators=(",", ":"),
            )
            await agent.put(key=key.encode("utf8"), value=None)
            mock_batcher.check_batches.assert_called_once()
