# type: ignore

import backoff
import botocore.exceptions
import json
import logging
import random
import time
from aries_se_api_client.client import <PERSON>esApiClient
from aries_task_logger import AriesTaskLogInfo, configure_logging
from aries_utils.lifecycle_handler import Lifecycle<PERSON>and<PERSON>
from data_platform_config_api_client.stack import Stack<PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from elasticsearch7 import Elasticsearch as Elasticsearch7
from elasticsearch8 import Elasticsearch as Elasticsearch8
from flows_router.static import FlowData, FlowMeta, MessageType, UnsupportedMessage
from flows_router.swarm_utils import get_flow, get_job_specs
from omegaconf import OmegaConf
from pathlib import Path
from se_aws_batch_utils.aws_batch_utils import get_batch_client
from se_elastic_schema.models.tenant.provenance.sink_file_audit import SinkFileAudit
from se_elastic_schema.static.provenance import TaskStatus
from se_schema_meta import ID as META_ID
from se_sqs_utils.sqs_utils import (
    get_queue_url_by_name,
    get_sqs_client,
    get_sqs_messages,
    sqs_retriable_call,
)
from typing import Any

_config = OmegaConf.load(Path(__file__).parent.parent.joinpath("flows-router-config.yml"))

configure_logging(
    AriesTaskLogInfo(
        stack=_config.stack,
        task_name=_config.task.name,
        task_version=_config.task.version,
    ),
    level="DEBUG" if bool(int(_config.debug)) else "INFO",
)

log = logging.getLogger(__name__)


class FlowsRouterService:
    def __init__(self):
        self._lifecycle_handler = LifecycleHandler()
        self._sqs_client = get_sqs_client()
        self._batch_client = get_batch_client()
        self._stack_api = StackAPI(AriesApiClient(host=_config.data_platform_config_api_url))
        self._queues_refresh_interval_s = int(_config.queues_refresh_interval_s)
        self._registry_es_client = Elasticsearch7(_config.registry_elastic_url, verify_certs=False)
        self._tenant_es_client = Elasticsearch8(
            _config.elastic_url,
            api_key=_config.elastic_api_key,
            ca_certs=_config.elastic_ca_certs,
            verify_certs=bool(int(_config.elastic_verify_certs)),
            ssl_show_warn=bool(int(_config.elastic_verify_certs)),
        )

    def run(self) -> None:
        log.info("Starting router service...")
        while self._lifecycle_handler.running:
            queue_names = self._derive_flow_queue_names()
            if not queue_names:
                log.info(f"No queue names found. Sleeping for {self._queues_refresh_interval_s}")
                time.sleep(self._queues_refresh_interval_s)
                continue

            queue_names_iter = iter(queue_names)
            queue_name = next(queue_names_iter, None)

            execution_start = time.time()
            while self._lifecycle_handler.running:
                queue_url = get_queue_url_by_name(
                    sqs_client=self._sqs_client,
                    queue_name=queue_name,  # type: ignore
                )
                if not queue_url:
                    log.error(f"Queue doesn't exists. Skipping queue: {queue_name}")
                    queue_name = next(queue_names_iter, None)
                    if queue_name is None:
                        log.info("Queue Name is None, sleeping")
                        self._sleep_on_no_queue(start_time=execution_start)
                        break
                else:
                    log.info(f"Processing queue url: {queue_url}")
                    messages = get_sqs_messages(
                        sqs_client=self._sqs_client,
                        queue_url=queue_url,
                        wait_time_seconds=5,
                        max_messages=1,
                        attribute_names=["All"],
                    )

                    if not messages:
                        log.warning(f"Skipping queue: {queue_name}, no messages found")
                        queue_name = next(queue_names_iter, None)
                        if queue_name is None:
                            # The service scrolled through each queue and reached the end
                            # of it. Sleep for the remaining duration and then derive
                            # the queue names from starting.
                            log.info("Queue Name is None, sleeping")
                            self._sleep_on_no_queue(start_time=execution_start)
                            break
                    else:
                        log.info(f"Messages found in {queue_url}, processing....")
                        self._process_queue_messages(queue_url=queue_url, messages=messages)
                        continue

                    # If the execution is running for more than the refresh queue
                    # time set then break the loop. It will then fetch
                    # the queue names again using the config API.
                    if (time.time() - execution_start) >= self._queues_refresh_interval_s:
                        break

    def _derive_flow_queue_names(self) -> list[str] | None:
        data = self._stack_api.get_by_name(stack_name=_config.stack, tenant_paused=False)
        if data.content["paused"]:
            log.warning("Stack level pause is on! No queues to discover...")
            return []

        if not data.content["tenants"]:
            log.warning("No tenants found in stack level config api. Response: %s", data)
            return []

        queue_names = list(
            map(
                lambda x: f"{x['name']}-{_config.env}-flows",
                data.content["tenants"],
            )
        )

        random.shuffle(queue_names)
        return queue_names

    def _sleep_on_no_queue(self, start_time: float) -> None:
        """Applies the sleep on process by subracting the elapsed run time.

        :param start_time: Execution start time
        """
        sleep_duration = self._queues_refresh_interval_s - (time.time() - start_time)
        if sleep_duration > 0:
            log.info(f"Sleeping for {sleep_duration} seconds...")
            time.sleep(sleep_duration)
        else:
            log.info(
                f"Sleep duration is negative `{sleep_duration}` seconds. Continuing without sleep."
            )

    def _process_queue_messages(self, queue_url: str, messages: list[dict[str, Any]]) -> None:
        for message in messages:
            log.info("Processing MessageId: %s", message["MessageId"])
            log.debug("Message: %s", message)

            should_delete_message = False
            try:
                flow_data = self._get_flow_data(message)
                flow_meta = self._get_flow_meta(flow_data)
                audit_id = self._audit_flow_message(flow_meta)
                self._submit_flow_batch_job(flow_meta, audit_id)
                should_delete_message = True
            except UnsupportedMessage as e:
                log.exception(f"Unsupported message: {e}")
                should_delete_message = True
            except Exception as e:
                log.exception(f"Error processing message {message['MessageId']}: {e}")

            if should_delete_message:
                sqs_retriable_call(
                    self._sqs_client.delete_message,
                    QueueUrl=queue_url,
                    ReceiptHandle=message["ReceiptHandle"],
                )
                log.info("Deleted SQS message")

    def _audit_flow_message(self, flow_meta: FlowMeta) -> str | None:
        def _get_audit_key() -> str:
            prefix = None
            prefix_args = flow_meta.flow_args.get("prefix")

            if prefix_args:
                delta_in_days = flow_meta.flow_args.get("delta_in_days")
                date_prefix = ""
                if delta_in_days:
                    date_prefix = (datetime.today() - timedelta(days=int(delta_in_days))).strftime(
                        "%Y%m%d"
                    )

                prefix = Path(prefix_args).joinpath(date_prefix).as_posix()

            if flow_meta.s3_key == flow_meta.bundle_id and prefix:
                audit_key = prefix
            else:
                audit_key = flow_meta.file_url

            if audit_key.startswith("s3:"):
                audit_key = audit_key.replace(f"s3://{flow_meta.s3_bucket}/", "")

            return audit_key

        bundle = flow_meta.flow["bundle"]
        audit = bundle.get("audit", dict())
        if not audit:
            return None

        task_name, task_version = bundle["image"].split(":")

        audit_record = SinkFileAudit(
            **dict(
                tenant=flow_meta.client["id"],
                bucket=flow_meta.s3_bucket,
                key=_get_audit_key(),
                queued=datetime.utcnow().isoformat() + "Z",
                status=TaskStatus.QUEUED,
                triggeredBy="S3",
                dataSource=dict(name=bundle["name"]),
                taskName=task_name,
                taskVersion=task_version,
                constrained=False,
            )
        )
        audit_record.apply_steeleye_meta()
        audit_record = json.loads(
            audit_record.json(
                by_alias=True,
                exclude_none=True,
            )
        )
        res = self._tenant_es_client.create(
            id=audit_record[META_ID],
            index=SinkFileAudit.get_elastic_index_alias(tenant=flow_meta.client["id"]),
            body=audit_record,
            refresh=True,
        )
        log.info(f"Audit record created, res: {res}")
        return audit_record[META_ID]

    @staticmethod
    def _get_flow_data(message: dict) -> FlowData:
        def _get_message_type(msg: dict) -> str:
            try:
                root = json.loads(msg["Body"])
                if "Records" in root:
                    root = root["Records"][0]
                if "s3" in root:
                    return MessageType.S3
                if "bundle" in root:
                    return MessageType.API
                raise UnsupportedMessage("Unknown message type")
            except KeyError:
                raise UnsupportedMessage("Unhandled message type")

        # determine message type
        message_type = _get_message_type(msg=message)
        if MessageType.S3 == message_type:
            # Extract s3 bucket and key from s3 message
            root = json.loads(message["Body"])["Records"][0]
            bucket = root["s3"]["bucket"]["name"]
            key = root["s3"]["object"]["key"]
            return FlowData(s3_bucket=bucket, s3_key=key, flow_args=None)

        # Extract flow id, bundle id and flow args from api message
        root = json.loads(message["Body"])
        if "Records" in root:
            root = root["Records"][0]
        realm = root["realm"]
        bundle_id = root["bundle"]["id"]
        args = root["args"]
        return FlowData(s3_bucket=realm, s3_key=bundle_id, flow_args=args)

    def _get_flow_meta(self, flow_data: FlowData):
        s3_bucket = flow_data.s3_bucket
        flow_args = flow_data.flow_args
        if "/" in flow_data.s3_key:
            _, bundle_id, *_ = flow_data.s3_key.split("/")
        else:
            bundle_id = flow_data.s3_key
        if flow_args is None:
            flow_args = dict()
        if s3_bucket.startswith("s3://"):
            # a defense if a bucket comes in with the s3:// protocol prefix
            s3_bucket = s3_bucket[5:]
        flow_id = ":".join([flow_data.s3_bucket, bundle_id])
        file_url = f"s3://{flow_data.s3_bucket}/{flow_data.s3_key}"

        log.info(f"Use flow id {flow_id}")

        if flow_id.startswith("refinitiv"):
            # refinitiv flows are really platform flows
            flow_id = flow_id.replace("refinitiv", "platform", 1)

        # extract client id from realm
        client_id, _ = flow_id.split(".", 1)
        # fetch client given client id
        client = self._registry_es_client.get(index="client", id=client_id, doc_type="_doc").get(
            "_source"
        )
        # extract stack based on client realm
        realm, _ = flow_id.split(":", 1)

        # fetch flow given flow id or local flow if passed
        flow = get_flow(
            registry_client=self._registry_es_client, flow_id=flow_id, stack=_config.stack
        )

        # assign platform flow flag
        platform_flow = flow["bundle"].get("platform", False) and flow["realm"].startswith(
            "platform"
        )

        if platform_flow:
            log.info(f"Operating {flow['bundle']['name']} as platform flow")

        flow_meta = FlowMeta(
            flow_id=flow_id,
            flow_args=flow_args,
            bundle_id=bundle_id,
            flow=flow,
            client=client,
            stack=_config.stack,
            s3_bucket=s3_bucket,
            s3_key=flow_data.s3_key,
            file_url=file_url,
            origin=self.__class__.__name__,
        )
        return flow_meta

    @backoff.on_exception(
        backoff.expo,
        botocore.exceptions.ClientError,
        max_tries=3,
    )
    def _submit_flow_batch_job(self, flow_meta: FlowMeta, audit_id: str | None):
        flows_specs = self._stack_api.get_swarm_flow_specs(_config.stack).content
        job_specs = get_job_specs(flow_meta=flow_meta, flows_specs=flows_specs, audit_id=audit_id)

        log.debug("Submit batch job with specs: %s", job_specs)
        response = self._batch_client.submit_job(**job_specs)
        log.info(f"Batch response: {response.get('jobId')}")
        return response.get("jobId")


if __name__ == "__main__":
    FlowsRouterService().run()
