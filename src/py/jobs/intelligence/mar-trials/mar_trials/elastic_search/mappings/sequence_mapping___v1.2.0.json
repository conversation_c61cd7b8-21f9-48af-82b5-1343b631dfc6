{"mappings": {"dynamic": "strict", "properties": {"&ancestor": {"type": "keyword"}, "&cascadeId": {"type": "keyword"}, "&expiry": {"type": "date"}, "&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&invocationId": {"type": "keyword"}, "&key": {"type": "keyword"}, "&link": {"type": "keyword"}, "&model": {"type": "keyword"}, "&parent": {"type": "keyword"}, "&realm": {"type": "keyword"}, "&status": {"type": "keyword"}, "&taskId": {"type": "keyword"}, "&timestamp": {"type": "date"}, "&traitFqn": {"type": "keyword"}, "&uniqueProps": {"type": "keyword"}, "&updater": {"type": "keyword"}, "&user": {"type": "keyword"}, "&validationErrors": {"dynamic": true, "properties": {"action": {"type": "keyword"}, "category": {"type": "keyword"}, "code": {"type": "keyword"}, "field_name": {"type": "keyword"}, "field_path": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "message": {"type": "keyword"}, "modules_affected": {"type": "keyword"}, "severity": {"type": "keyword"}, "source": {"type": "keyword"}}, "type": "nested"}, "&version": {"type": "integer"}, "excludes": {"type": "keyword"}}}, "settings": {"gc_deletes": 0, "mapping.ignore_malformed": true, "max_inner_result_window": 1000, "number_of_replicas": 0, "number_of_shards": 1, "refresh_interval": -1, "analysis": {"analyzer": {"all": {"filter": ["lowercase", "asciifolding"], "tokenizer": "whitespace", "type": "custom"}, "alphanum": {"char_filter": ["alphanum_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "alphaonly": {"char_filter": ["alpha_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "content_en": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph"], "tokenizer": "whitespace", "type": "custom"}, "content_en_stemmed": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph", "english_stop", "english_stemmer"], "tokenizer": "whitespace", "type": "custom"}, "default": {"type": "standard"}, "norm": {"filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "numonly": {"char_filter": ["digit_only_filter", "zero_remove_filter"], "tokenizer": "keyword", "type": "custom"}, "rfc5322": {"char_filter": ["rfc5322_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}}, "char_filter": {"alpha_only_filter": {"pattern": "[^\\pL]", "replacement": "", "type": "pattern_replace"}, "alphanum_only_filter": {"pattern": "[^(\\d|\\pL)]", "replacement": "", "type": "pattern_replace"}, "digit_only_filter": {"pattern": "[^\\d]", "replacement": "", "type": "pattern_replace"}, "rfc5322_only_filter": {"pattern": "[^(\\d\\pL\\Q!#$%&*+-/=?^_{|}~@.\\E)]", "replacement": "", "type": "pattern_replace"}, "zero_remove_filter": {"pattern": "\\b0+", "replacement": "", "type": "pattern_replace"}}, "filter": {"autocomplete_filter": {"max_gram": "20", "min_gram": "1", "type": "edge_ngram"}, "email": {"patterns": ["([^@]+)", "(\\p{L}+)", "(\\d+)", "@(.+)"], "preserve_original": true, "type": "pattern_capture"}, "english_stemmer": {"language": "english", "type": "stemmer"}, "english_stop": {"stopwords": "_english_", "type": "stop"}, "word_splitter": {"catenate_all": true, "generate_number_parts": false, "preserve_original": true, "split_on_numerics": false, "type": "word_delimiter_graph"}}}}}