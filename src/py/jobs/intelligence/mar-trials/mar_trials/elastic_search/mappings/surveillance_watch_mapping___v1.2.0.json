{"mappings": {"dynamic": "strict", "properties": {"&ancestor": {"type": "keyword"}, "&cascadeId": {"type": "keyword"}, "&expiry": {"type": "date"}, "&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&invocationId": {"type": "keyword"}, "&key": {"type": "keyword"}, "&link": {"type": "keyword"}, "&model": {"type": "keyword"}, "&parent": {"type": "keyword"}, "&realm": {"type": "keyword"}, "&status": {"type": "keyword"}, "&taskId": {"type": "keyword"}, "&timestamp": {"type": "date"}, "&traitFqn": {"type": "keyword"}, "&uniqueProps": {"type": "keyword"}, "&updater": {"type": "keyword"}, "&user": {"type": "keyword"}, "&validationErrors": {"dynamic": true, "properties": {"action": {"type": "keyword"}, "category": {"type": "keyword"}, "code": {"type": "keyword"}, "field_name": {"type": "keyword"}, "field_path": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "message": {"type": "keyword"}, "modules_affected": {"type": "keyword"}, "severity": {"type": "keyword"}, "source": {"type": "keyword"}}, "type": "nested"}, "&version": {"type": "integer"}, "actionEmails": {"type": "keyword"}, "actionType": {"type": "keyword"}, "backtest": {"type": "keyword"}, "backtestPeriod": {"type": "keyword"}, "conditionType": {"type": "keyword"}, "continuousActionHours": {"type": "integer"}, "continuousFrequencyTime": {"properties": {"hours": {"type": "integer"}, "minutes": {"type": "integer"}}}, "createdBy": {"type": "keyword"}, "createdByAdmin": {"type": "boolean"}, "createdOn": {"type": "date"}, "data": {"type": "keyword"}, "defaultAssigneeId": {"type": "keyword"}, "defaultAssigneeName": {"type": "keyword"}, "executionDetails": {"properties": {"lastExecution": {"type": "date"}, "lastSuccessfulExecution": {"type": "date"}, "nextExecution": {"type": "date"}}}, "frequencyType": {"type": "keyword"}, "jurisdictions": {"properties": {"businessLine": {"type": "keyword"}, "country": {"type": "keyword"}, "legalEntity": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}}}, "name": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "pendingChange": {"type": "boolean"}, "priority": {"type": "keyword"}, "query": {"properties": {"&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "actionEmails": {"type": "keyword"}, "analytics": {"properties": {"confidenceScore": {"type": "double"}, "excludedClassifications": {"type": "keyword"}, "excludedZones": {"type": "keyword"}, "lexicaCategories": {"type": "keyword"}}}, "attachments": {"properties": {"fileNames": {"type": "keyword"}, "fileTypes": {"type": "keyword"}, "rangeType": {"type": "keyword"}, "sizeInBytes": {"type": "integer"}, "sizeInBytesRangeMax": {"type": "integer"}, "sizeInBytesRangeMin": {"type": "integer"}}}, "bestEx": {"properties": {"excludeConvertedCurrency": {"type": "boolean"}, "priceField": {"type": "keyword"}, "rangeMax": {"type": "integer"}, "rangeMaxDecimal": {"type": "double"}, "rangeMin": {"type": "integer"}, "rangeMinDecimal": {"type": "double"}, "rangeType": {"type": "keyword"}, "speedOfExecutionTime": {"type": "integer"}}}, "counterparties": {"properties": {"field": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "model": {"type": "keyword"}, "rangeMax": {"type": "double"}, "rangeMin": {"type": "double"}, "rangeType": {"type": "keyword"}, "value": {"type": "double"}, "values": {"type": "keyword"}}}, "countries": {"type": "keyword"}, "created": {"type": "date"}, "createdBy": {"type": "keyword"}, "dataSources": {"type": "keyword"}, "description": {"type": "keyword"}, "falsePositiveReduction": {"properties": {"excludedClassifications": {"properties": {"className": {"type": "keyword"}, "confidenceScore": {"type": "double"}}}, "excludedZones": {"properties": {"className": {"type": "keyword"}, "confidenceScore": {"type": "double"}}}}}, "fields": {"properties": {"chosenAgg": {"type": "keyword"}, "chosenCurrency": {"type": "keyword"}, "chosenDateTimeFormat": {"type": "keyword"}, "columnOrMetric": {"type": "keyword"}, "displayName": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "filter": {"type": "keyword"}, "nested": {"type": "boolean"}, "nestedPath": {"type": "keyword"}, "propertyId": {"type": "keyword"}}}, "filter": {"properties": {"chunks": {"properties": {"category": {"type": "keyword"}, "f": {"type": "keyword"}, "id": {"type": "keyword"}}}, "flangVersion": {"type": "keyword"}}}, "filters": {"type": "keyword"}, "firms": {"type": "keyword"}, "infoBarrier": {"properties": {"aiBooster": {"type": "integer"}, "involvingProjectMembers": {"type": "boolean"}, "maxRecipients": {"type": "integer"}, "projectSelection": {"properties": {"chunks": {"properties": {"category": {"type": "keyword"}, "f": {"type": "keyword"}, "id": {"type": "keyword"}}}, "flangVersion": {"type": "keyword"}}}}}, "instruments": {"properties": {"field": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "model": {"type": "keyword"}, "rangeMax": {"type": "double"}, "rangeMin": {"type": "double"}, "rangeType": {"type": "keyword"}, "value": {"type": "double"}, "values": {"type": "keyword"}}}, "kind": {"type": "keyword"}, "lexica": {"properties": {"&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "category": {"type": "keyword"}, "excludedTerms": {"properties": {"fuzziness": {"type": "integer"}, "searchType": {"type": "keyword"}, "slop": {"type": "integer"}, "stemmed": {"type": "boolean"}, "term": {"type": "keyword"}}}, "excludedTermsOperator": {"type": "keyword"}, "fuzziness": {"type": "integer"}, "language": {"type": "keyword"}, "name": {"type": "keyword"}, "searchType": {"type": "keyword"}, "slop": {"type": "integer"}, "stemmed": {"type": "boolean"}, "subCategory": {"type": "keyword"}, "term": {"type": "keyword"}, "tmpExcludeTopics": {"type": "keyword"}}}, "lexicaBehaviour": {"properties": {"id": {"type": "keyword"}, "languages": {"type": "keyword"}, "name": {"type": "keyword"}}}, "marketAbuseReportType": {"type": "keyword"}, "module": {"type": "keyword"}, "name": {"type": "keyword"}, "neuralThresholds": {"properties": {"l1Score": {"type": "double"}, "l2Score": {"type": "double"}, "l3Score": {"type": "double"}}}, "orderDetails": {"properties": {"field": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "model": {"type": "keyword"}, "rangeMax": {"type": "double"}, "rangeMin": {"type": "double"}, "rangeType": {"type": "keyword"}, "value": {"type": "double"}, "values": {"type": "keyword"}}}, "paused": {"type": "boolean"}, "people": {"properties": {"&hash": {"type": "keyword"}, "&id": {"type": "keyword"}, "&key": {"type": "keyword"}, "betweenFilter": {"properties": {"field": {"type": "keyword"}, "model": {"type": "keyword"}, "values": {"type": "keyword"}}}, "filter": {"type": "keyword"}, "filterType": {"type": "keyword"}, "filters": {"properties": {"field": {"type": "keyword"}, "model": {"type": "keyword"}, "values": {"type": "keyword"}}}, "participantQueryType": {"type": "keyword"}, "peopleFilter": {"analyzer": "standard", "type": "text"}}}, "refineOptions": {"type": "keyword"}, "reportContents": {"type": "keyword"}, "restrictedListId": {"type": "keyword"}, "schedule": {"type": "boolean"}, "shared": {"type": "boolean"}, "template": {"properties": {"templateType": {"type": "keyword"}, "thresholds": {"properties": {"sampleBasis": {"type": "keyword"}, "samplePercentage": {"type": "integer"}, "sampleSize": {"type": "integer"}}}}}, "thresholds": {"type": "keyword"}, "time": {"properties": {"daysOfWeek": {"type": "keyword"}, "field": {"type": "keyword"}, "hourEnd": {"type": "integer"}, "hourStart": {"type": "integer"}, "rangeType": {"type": "keyword"}, "timeOfDayEndInMilliseconds": {"type": "integer"}, "timeOfDayStartInMilliseconds": {"type": "integer"}}}, "tradeDetails": {"properties": {"field": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "model": {"type": "keyword"}, "rangeMax": {"type": "double"}, "rangeMin": {"type": "double"}, "rangeType": {"type": "keyword"}, "value": {"type": "double"}, "values": {"type": "keyword"}}}, "traders": {"properties": {"field": {"type": "keyword"}, "fieldType": {"type": "keyword"}, "model": {"type": "keyword"}, "rangeMax": {"type": "double"}, "rangeMin": {"type": "double"}, "rangeType": {"type": "keyword"}, "value": {"type": "double"}, "values": {"type": "keyword"}}}, "updated": {"type": "date"}, "updatedBy": {"type": "keyword"}, "watch_filter": {"properties": {"chunks": {"properties": {"category": {"type": "keyword"}, "f": {"type": "keyword"}, "id": {"type": "keyword"}}}, "flangVersion": {"type": "keyword"}}}}}, "queryType": {"fields": {"search": {"analyzer": "all", "type": "text"}, "text": {"analyzer": "norm", "type": "text"}}, "type": "keyword"}, "scheduleDetails": {"properties": {"dayOfMonth": {"type": "integer"}, "daysOfWeek": {"type": "keyword"}, "interval": {"type": "integer"}, "monthlyRecurrence": {"type": "keyword"}, "recurrence": {"type": "keyword"}, "timeOfDay": {"type": "keyword"}, "timeZone": {"type": "keyword"}, "weekOfYear": {"type": "integer"}}}, "status": {"type": "keyword"}, "type": {"type": "keyword"}}}, "settings": {"gc_deletes": 0, "mapping.ignore_malformed": true, "max_inner_result_window": 1000, "number_of_replicas": 0, "number_of_shards": 1, "refresh_interval": -1, "analysis": {"analyzer": {"all": {"filter": ["lowercase", "asciifolding"], "tokenizer": "whitespace", "type": "custom"}, "alphanum": {"char_filter": ["alphanum_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "alphaonly": {"char_filter": ["alpha_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "content_en": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph"], "tokenizer": "whitespace", "type": "custom"}, "content_en_stemmed": {"filter": ["lowercase", "email", "word_splitter", "flatten_graph", "english_stop", "english_stemmer"], "tokenizer": "whitespace", "type": "custom"}, "default": {"type": "standard"}, "norm": {"filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}, "numonly": {"char_filter": ["digit_only_filter", "zero_remove_filter"], "tokenizer": "keyword", "type": "custom"}, "rfc5322": {"char_filter": ["rfc5322_only_filter"], "filter": ["lowercase", "asciifolding"], "tokenizer": "keyword", "type": "custom"}}, "char_filter": {"alpha_only_filter": {"pattern": "[^\\pL]", "replacement": "", "type": "pattern_replace"}, "alphanum_only_filter": {"pattern": "[^(\\d|\\pL)]", "replacement": "", "type": "pattern_replace"}, "digit_only_filter": {"pattern": "[^\\d]", "replacement": "", "type": "pattern_replace"}, "rfc5322_only_filter": {"pattern": "[^(\\d\\pL\\Q!#$%&*+-/=?^_{|}~@.\\E)]", "replacement": "", "type": "pattern_replace"}, "zero_remove_filter": {"pattern": "\\b0+", "replacement": "", "type": "pattern_replace"}}, "filter": {"autocomplete_filter": {"max_gram": "20", "min_gram": "1", "type": "edge_ngram"}, "email": {"patterns": ["([^@]+)", "(\\p{L}+)", "(\\d+)", "@(.+)"], "preserve_original": true, "type": "pattern_capture"}, "english_stemmer": {"language": "english", "type": "stemmer"}, "english_stop": {"stopwords": "_english_", "type": "stop"}, "word_splitter": {"catenate_all": true, "generate_number_parts": false, "preserve_original": true, "split_on_numerics": false, "type": "word_delimiter_graph"}}}}}