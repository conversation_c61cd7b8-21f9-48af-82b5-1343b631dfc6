{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "940cfe0d-e9a0-9afd-411c-689d9b7b12ac", "&key": "SurveillanceWatch:940cfe0d-e9a0-9afd-411c-689d9b7b12ac:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "front_running_v2___test_case_2_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"adv\": null, \"frontrunOrderVolume\": 5000000, \"evaluationType\": \"Trader\", \"flow\": \"Prop vs. Client\", \"priceImprovement\": true, \"timeWindow\": {\"unit\": \"minutes\", \"value\": 1}, \"volumeDifference\": 0.01}", "marketAbuseReportType": "FRONT_RUNNING_V2", "name": "test_case_2_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.frontrunnng2.2.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}