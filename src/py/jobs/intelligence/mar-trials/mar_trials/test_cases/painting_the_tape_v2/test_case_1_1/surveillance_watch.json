{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "0d6a3f52-59bd-07ae-8af8-511c6a49c81b", "&key": "SurveillanceWatch:0d6a3f52-59bd-07ae-8af8-511c6a49c81b:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "painting_the_tape_v2___test_case_1_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"directionality\": \"Only Buys, or Only Sells\", \"evaluationType\": \"Executing Entity\", \"percentageAdv\": \"0.4\", \"marketDataEvaluationType\": \"Market Day Traded Volume\", \"minOrderCount\": \"4\", \"sameCounterparty\": \"false\", \"timeWindow\": {\"unit\": \"minutes\", \"value\": \"12\"}}", "marketAbuseReportType": "PAINTING_THE_TAPE_V2", "name": "test_case_1_1", "filters": "sourceKey in ['steeleyeblotter.mar.ramping.1.csv']"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}