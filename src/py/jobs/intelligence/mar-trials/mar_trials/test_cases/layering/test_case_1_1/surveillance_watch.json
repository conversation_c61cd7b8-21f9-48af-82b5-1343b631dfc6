{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "ebedf4a2-0544-527b-03f2-668d7f869c41", "&key": "SurveillanceWatch:ebedf4a2-0544-527b-03f2-668d7f869c41:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "layering___test_case_1_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"orderTimeWindow\": {\"unit\": \"milliseconds\", \"value\": 0}, \"percentageOppositeOrderVolume\": 0.01, \"detectLayeringBehaviour\": false, \"detectSuccessfulAttempt\": false}", "marketAbuseReportType": "LAYERING", "name": "test_case_1_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.layering.1.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}