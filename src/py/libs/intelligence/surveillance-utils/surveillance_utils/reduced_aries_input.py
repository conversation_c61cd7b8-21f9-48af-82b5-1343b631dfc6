from aries_io_event.io_param import IOParamFieldSet
from pydantic import BaseModel, Field


class ReducedWorkflowFieldSet(BaseModel):
    name: str = Field(
        ..., description="Workflow name as per Platform's config DB table Workflow", copyToAll=False
    )
    tenant: str = Field(
        ...,
        description="Tenant's name as per Platform's config DB table Tenant",
        copyToAll=False,
    )


class ReducedAriesTaskInput(BaseModel):
    workflow: ReducedWorkflowFieldSet = Field(..., description="Workflow information")
    input_param: IOParamFieldSet = Field(..., description="Input parameters")
