class RestrictedListFields:
    ID = "id"
    ALTERNATE_ID = "alternateId"
    DESCRIPTION = "description"
    DETECTION_TYPE = "detectionType"
    DETECTED_PARTY = "detectedParty"
    NAME = "name"
    OWNER = "owner"
    LIST_TYPE = "listType"
    UPDATED_DATE_TIME = "updatedDateTime"
    CREATED_DATE_TIME = "createdDateTime"
    PROVENANCE = "provenance"
    CHANGE_REASON = "changeReason"
    DATA_SOURCE = "dataSource"
    DATE_FROM = "fromDateTime"
    DATE_TO = "toDateTime"
    IDENTIFIER = "identifier"
    IDENTIFIER_TYPE = "identifierType"
    PARTY = "party"
    PERMANENCY = "permanency"
    LABEL = "label"
    MINIMUM_SIZE = "minimumSize"
    MINIMUM_SIZE_TYPE = "minimumSizeType"
    MINIMUM_SIZE_CURRENCY = "minimumSizeCurrency"
    SOURCE_KEY = "sourceKey"
    WATCH_REASON_ID = "watchReasonId"
    WATCH_REASON = "watchReason"
    RESTRICTED_LIST_ID = "restrictedListId"
    RESTRICTIONS_LIST_ID = "restrictionsListId"


class DetectedParty:
    CLIENT = "client"
    TRADER = "trader"
    PORTFOLIO_MANAGER = "portfolioManager"
