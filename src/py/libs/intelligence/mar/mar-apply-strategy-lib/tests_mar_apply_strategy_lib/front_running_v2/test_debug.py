import pytest
from aries_task_link.models import AriesTaskInput
from front_running_v2_apply_strategy.apply_strategy import ApplyStrategy
from pathlib import Path
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from surveillance_utils.test_mock_helpers import load_ndjson_into_list
from surveillance_utils.watch_execution_handling import fetch_watch_execution_record
from surveillance_utils.watch_handling import fetch_watch_record
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")


def sample_aries_task_input():
    return AriesTaskInput.validate(
        {
            "workflow": {
                "start_timestamp": "2024-04-24T10:11:32.249756",
                "name": "mar_itv3_refinitiv",
                "stack": "dev-shared-2",
                "tenant": "mares8",
                "trace_id": 1234,
            },
            "task": {"name": "mar-apply-strategy", "version": "latest", "success": True},
            "input_param": {
                "params": {
                    "group_index": 0,
                    "group_file_uri": "s3://mares8.dev.steeleye.co/aries/ingest/mar_itv3_refinitiv/2024/04/24/mares8_itv3_algo_1/mar-create-group/9e22266af540f651e77eb3f30890c1acb0eb5b2b0bc6eb07e2107e334614933c___groups.parquet",
                    "watch_metadata_file_uri": "s3://mares8.dev.steeleye.co/aries/ingest/mar_itv3_refinitiv/2024/04/24/mares8_itv3_algo_1/mar-create-group/9e22266af540f651e77eb3f30890c1acb0eb5b2b0bc6eb07e2107e334614933c___watch_metadata.json",
                }
            },
        }
    )


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerFrontRunningV2:
    def test_case_debug(self, helpers, mock_apply_strategy_kwargs):
        thresholds = {
            "evaluationType": "Portfolio Manager",
            "flow": "Client vs. Client",
            "timeWindow": {"unit": "minutes", "value": 10},
            "volumeDifference": 0.1,
            "priceImprovement": True,
        }

        filters = {
            "bool": {
                "filter": [
                    {"terms": {"dataSourceName": ["SteelEyeTradeBlotter"]}},
                    {"range": {"&timestamp": {"gte": "2024-07-12T14:34:37.897000Z"}}},
                ],
                "must_not": [
                    {
                        "terms": {
                            "&id": {
                                "index": "barings-surveillance_exclude_by_id-alias",
                                "id": "ac14088c-80d8-4876-8569-a5ddc9c8ae57",
                                "path": "excludes",
                            }
                        }
                    },
                    {
                        "terms": {
                            "&parent": {
                                "index": "barings-surveillance_exclude_by_id-alias",
                                "id": "533adb0e-cd5a-449e-b9f9-ba80756225e8",
                                "path": "excludes",
                            }
                        }
                    },
                ],
            }
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)

        task_input = sample_aries_task_input()
        RECORD_HANDLER = SlimRecordHandler()

        surv_watch = fetch_watch_record(
            context.watch_id, task_input.workflow.tenant, RECORD_HANDLER
        )
        surv_watch_execution = fetch_watch_execution_record(
            context.watch_execution_id, task_input.workflow.tenant, RECORD_HANDLER
        )

        apply_strategy_args = {
            "group_file_path": task_input.input_param.params["group_file_uri"],
            "group_index": task_input.input_param.params["group_index"],
            "surv_watch": surv_watch,
            "surv_watch_execution": surv_watch_execution,
            "aries_task_input": task_input,
            "cloud_provider": "aws",
            "lake_prefix": "s3://mares8.dev.steeleye.co/",
        }

        strategy = ApplyStrategy(**apply_strategy_args)

        # Explicit mock to s3 upload
        strategy.auditor.write_audit_to_cloud = MagicMock()
        strategy.auditor.write_alerts_and_scenarios_to_cloud = MagicMock()
        strategy.write_alerts_and_scenarios_to_cloud = MagicMock()

        strategy.run()
        scenarios = load_ndjson_into_list(strategy.scenario_local_file_path)

        assert len(scenarios) > 0
