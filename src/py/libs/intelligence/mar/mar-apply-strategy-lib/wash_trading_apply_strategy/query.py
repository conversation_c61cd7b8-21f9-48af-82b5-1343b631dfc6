# type: ignore # TODO: Remove this
import itertools
import pandas as pd
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderExecutionsQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import (  # noqa: E501
    NewColumns,
    OrderField,
    OrderStatus,
)
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from se_elastic_schema.models.tenant.mifid2.order import Order
from typing import List
from wash_trading_apply_strategy.models import EVALUATION_TYPE_FIELD_MAPPING
from wash_trading_apply_strategy.static import (
    DFColumns,
    NumberOfCounterparties,
    ThresholdsNames,
)


class Queries(BaseQuery):
    def __init__(self, context: StrategyContext, audit: Audit):
        super().__init__(context=context, audit=audit)

        self._th_record_type = self.context.thresholds.dict().get(ThresholdsNames.RECORD_TYPE)

        self._th_max_price_difference = self.context.thresholds.dict().get(
            ThresholdsNames.MAX_PRICE_DIFFERENCE
        )
        self._th_n_counterparties = self.context.thresholds.dict().get(
            ThresholdsNames.NUMBER_OF_COUNTERPARTIES
        )
        self._th_day_traded_notional = self.context.thresholds.dict().get(
            ThresholdsNames.DAY_TRADED_NOTIONAL
        )

        if self._th_max_price_difference is not None:
            self._th_max_price_difference = self._th_max_price_difference / 100 / 100
        self._market_data_client = get_market_client(tenant=context.tenant)

    # TODO: Rename to cases_to_analyse once the ApplyStrategy is ready too.
    # Method is prefixed with `new` because want to keep running the old algorithm
    # using the Mar Wrapper :)
    def new_cases_to_analyse(self, search_size: int):
        """Retrieves the required data for the Wash Trading create group
        algorithm."""

        initial_query = self.get_initial_query(
            self._get_query(),
            inspect_required_fields=True,
        )
        # Get only the required fields that we need in the grouping.
        initial_query.size(search_size)
        initial_query.includes(
            [OrderField.EXC_DTL_BUY_SELL_IND, *OrderField.get_instrument_fields()]
        )
        yield from self._sdp_repository.search_after_query_yield(
            query=initial_query,
            model_index=Order.get_elastic_index_alias(tenant=self.context.tenant),
        )

    def get_instrument_orders(self, instrument_id: str, search_size: int):
        """Retrieves the required data for the Wash Trading apply strategy
        algorithm."""
        initial_query = self.get_initial_query(
            self._get_query(),
            inspect_required_fields=True,
        )

        initial_query.size(search_size)
        initial_query.instrument_id(instrument_id)

        yield from self._sdp_repository.search_after_query_yield(
            query=initial_query,
            model_index=Order.get_elastic_index_alias(tenant=self.context.tenant),
        )

    def _get_query(self, inst_comb=None) -> OrderExecutionsQuery:
        query = OrderExecutionsQuery()

        query.order_status([OrderStatus.FILL, OrderStatus.PARF])

        common_fields = [
            OrderField.META_KEY,
            OrderField.EXC_DTL_BUY_SELL_IND,
            OrderField.TS_TRADING_DATE_TIME,
            OrderField.PC_FD_PRICE,
            OrderField.PC_FD_TRD_QTY,
        ]

        include_fields = [
            *common_fields,
            *OrderField.get_instrument_fields(),
            OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE,
        ]

        required_fields = [*common_fields, OrderField.get_instrument_fields()]

        if self._th_max_price_difference is not None:
            required_fields.append(OrderField.PC_FD_TRD_QTY)

        if getattr(self.context.thresholds, "evaluationType", None):
            evaluation_field = EVALUATION_TYPE_FIELD_MAPPING[self.context.thresholds.evaluationType]
            include_fields.append(evaluation_field)
            required_fields.append(evaluation_field)

        if getattr(self.context.thresholds, "minimumNotionalValueCurrency", None):
            currency_field = OrderField.get_best_exc_trx_ecb_ref_rate_ccy(
                currency=self.context.thresholds.minimumNotionalValueCurrency
            )
            include_fields.append(currency_field)
            required_fields.append(currency_field)

        if self._th_n_counterparties == NumberOfCounterparties.SINGLE:
            required_fields.append(OrderField.COUNTERPARTY_ID)
            include_fields.append(OrderField.COUNTERPARTY_ID)

        query.includes(include_fields)
        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=required_fields)

        if self._th_record_type is not None:
            query.record_type(self._th_record_type)

        if inst_comb:
            query.instrument_id(inst_comb)

        return query

    def get_additional_fields_for_results(self, df: pd.DataFrame) -> pd.DataFrame:
        keys = list(set(itertools.chain(*df[DFColumns.EXECUTIONS_KEYS].tolist())))

        query = OrderExecutionsQuery()
        query.size(OrderExecutionsQuery.SIZE)
        query.includes(
            [
                OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY,
                OrderField.CLIENT_IDENT_CLIENT,
                OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
                OrderField.INST_EXT_BEST_EX_ASSET_CLASS_SUB,
                OrderField.INST_FULL_NAME,
                OrderField.EXC_DTL_BUY_SELL_IND,
                OrderField.EXC_DTL_ORD_TYPE,
                OrderField.META_KEY,
                OrderField.ORD_IDENT_ID_CODE,
                OrderField.PARTICIPANTS,
                OrderField.TRADER,
                *OrderField.get_client_fields(),
                *OrderField.get_involved_parties_fields(),
                *OrderField.get_venue_fields(),
            ]
        )
        query.key(keys)

        query.order_status([OrderStatus.FILL, OrderStatus.PARF])

        result = self._sdp_repository.search_after_query(query)

        if result.empty:
            return pd.DataFrame()

        def get_fields(x: pd.Series, columns: List[str]) -> List[str]:
            fields_df = result.loc[
                result[OrderField.META_KEY].isin(x),
                result.columns.isin(columns),
            ]

            fields = pd.unique(fields_df.values.ravel("K"))

            fields: list[str] = sorted(fields[pd.notnull(fields)].tolist())

            return fields

        df.loc[:, DFColumns.CLIENT_NAMES] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(x=x, columns=[NewColumns.CLIENT])
        )

        df.loc[:, DFColumns.IMPLIED_PL_CURRENCY] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(x=x, columns=[OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE_CURRENCY])
        )

        df.loc[:, DFColumns.IMPLIED_PL_CURRENCY] = df.loc[:, DFColumns.IMPLIED_PL_CURRENCY].apply(
            lambda x: x[0] if len(x) == 1 else x
        )

        # Get involved parties based on executions keys
        df.loc[:, DFColumns.INVOLVED_PARTIES] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(
                x=x,
                columns=[
                    NewColumns.CLIENT,
                    NewColumns.TRADER_NAME,
                    OrderField.COUNTERPARTY_NAME,
                ],
            )
        )

        df.loc[:, DFColumns.NUMBER_OF_EXECUTIONS] = len(
            set(df.loc[:, DFColumns.EXECUTIONS_KEYS].iloc[0])
        )

        df.loc[:, DFColumns.NUMBER_OF_ORDERS] = len(
            df.loc[:, DFColumns.EXECUTIONS_KEYS]
            .apply(lambda x: get_fields(x=x, columns=[OrderField.ORD_IDENT_ID_CODE]))
            .iloc[0]
        )

        df.loc[:, DFColumns.ORDER_TYPES] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(x=x, columns=[OrderField.EXC_DTL_ORD_TYPE])
        )

        df.loc[:, DFColumns.TRADER_NAMES] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(x=x, columns=[NewColumns.TRADER_NAME])
        )

        df.loc[:, DFColumns.ULIMATE_VENUES] = df.loc[:, DFColumns.EXECUTIONS_KEYS].apply(
            lambda x: get_fields(x=x, columns=[OrderField.TRX_DTL_ULTIMATE_VENUE])
        )

        df = df.merge(result, how="left", on=OrderField.META_KEY).filter(
            items=[
                OrderField.META_KEY,
                OrderField.INST_EXT_BEST_EX_ASSET_CLASS_MAIN,
                OrderField.INST_EXT_BEST_EX_ASSET_CLASS_SUB,
                OrderField.INST_FULL_NAME,
                DFColumns.IMPLIED_PL_CURRENCY,
                DFColumns.CLIENT_NAMES,
                DFColumns.INVOLVED_PARTIES,
                DFColumns.NUMBER_OF_EXECUTIONS,
                DFColumns.NUMBER_OF_ORDERS,
                DFColumns.ORDER_TYPES,
                DFColumns.TRADER_NAMES,
                DFColumns.ULIMATE_VENUES,
            ],
            axis=1,
        )

        return df

    def get_market_data(self, ric: str, start_timestamp: pd.Timestamp) -> pd.DataFrame:
        """Fetches EOD stats files with daily traded notional.

        :param ric: id to fetch EOD rolling stats files
        :return: pandas dataframe with EOD data
        """

        data: pd.DataFrame = self._market_data_client.get_market_data_stats(
            instrument_ric=ric,
            start_date=start_timestamp,
            end_date=self.market_data_end_date,
            use_time_margin=False,
        )

        return data
