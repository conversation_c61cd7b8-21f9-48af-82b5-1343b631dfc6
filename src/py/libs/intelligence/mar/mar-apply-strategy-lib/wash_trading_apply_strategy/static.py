import operator
from enum import Enum
from market_abuse_algorithms.data_source.repository.market_data.static import EODStatsDataColumns


class ThresholdsNames(str, Enum):
    DAY_TRADED_NOTIONAL = "dayTradedNotional"
    DAY_TRADED_NOTIONAL_CURRENCY = "dayTradedNotionalCurrency"
    DAY_TRADED_NOTIONAL_OPERATOR = "dayTradedNotionalOperator"
    EXCLUDE_MATCHING_TIMESTAMPS = "excludeMatchingTimestamps"
    MAX_PRICE_DIFFERENCE = "maxPriceDifference"
    MAX_TIME_WINDOW = "maxTimeWindow"
    MAX_VOLUME_DIFFERENCE = "maxVolumeDifference"
    MINIMUM_TRADED_QUANTITY = "minimumTradedQuantity"
    NUMBER_OF_COUNTERPARTIES = "numberOfCounterparties"
    RECORD_TYPE = "recordType"


class NumberOfCounterparties(str, Enum):
    SINGLE = "single"
    MULTIPLE = "multiple"


class DFColumns:
    BUYS_QUANTITY = "buysQuantity"
    BUYS_QUANTITY_SUM = "buysQuantitySum"
    CLIENT_NAMES = "clientNames"
    EXECUTED_BUY_QUANTITY = "executedBuyQuantity"
    EXECUTED_SELL_QUANTITY = "executedSellQuantity"
    EXECUTIONS_KEYS = "executionsKeys"
    IMPLIED_PL = "impliedPL"
    IMPLIED_PL_CURRENCY = "impliedPLCurrency"
    INSTRUMENT_ID = "instrumentId"
    INVOLVED_PARTIES = "involvedParties"
    MAX_PRICE_DIFFERENCE = "maxPriceDifference"
    NUMBER_OF_EXECUTIONS = "numberOfExecutions"
    NUMBER_OF_ORDERS = "numberOfOrders"
    ORDER_TYPES = "orderTypes"
    RIC = "ric"
    PRICE_DIFFERENCE = "priceDifference"
    SELLS_QUANTITY = "sellsQuantity"
    SELLS_QUANTITY_SUM = "sellsQuantitySum"
    TIME_DIFFERENCE = "timeDifference"
    TRADER_NAMES = "traderNames"
    TS_TRADE_TIME_SEC = "tradingTimeSec"
    TS_TRADE_TIME_AFTER = "tsTradeTimeAfter"
    VOLUME_PERCENTAGE_DIFFERENCE = "volumePercentageDifference"
    ULIMATE_VENUES = "ultimateVenues"


class NotionalOperator(str, Enum):
    GREATER = "Greater Than"
    GREATER_EQUAL = "Greater than or Equal To"
    LESS = "Less Than"
    LESS_EQUAL = "Less than or Equal To"


DAY_TRADED_NOTIONAL_MAP = {
    "AUD": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_AUD,
    "CHF": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_CHF,
    "EUR": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_EUR,
    "GBP": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_GBP,
    "JPY": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_JPY,
    "SGD": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_SGD,
    "USD": EODStatsDataColumns.DAILY_TRADED_NOTIONAL_USD,
}

NOTIONAL_OPERATOR_MAP = {
    NotionalOperator.GREATER: operator.gt,
    NotionalOperator.GREATER_EQUAL: operator.ge,
    NotionalOperator.LESS: operator.lt,
    NotionalOperator.LESS_EQUAL: operator.le,
}
