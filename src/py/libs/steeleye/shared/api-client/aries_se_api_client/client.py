import abc
import asyncio
import backoff
import httpx
from aries_se_api_client.auth.abstract_auth_client import AbstractAuthClient
from aries_se_api_client.base import AbstractAPI, EndPoint, ParsedResponse
from aries_se_api_client.types import ResponseType
from typing import Any, Awaitable, Sequence, Union


class GatewayTimeoutException(Exception): ...


class AriesAbstractApiClient(abc.ABC):
    _auth_client: AbstractAuthClient

    def __init__(self, auth_client: AbstractAuthClient | None = None):
        self._auth_client = auth_client  # type: ignore

    @abc.abstractmethod
    def close(self):
        raise NotImplementedError()

    @backoff.on_exception(backoff.expo, exception=GatewayTimeoutException, max_tries=3)
    def call_api(
        self,
        api: AbstractAPI,
        endpoint: EndPoint[ResponseType],
        path_params: dict[str, Any] | None = None,
        query_param: dict[str, Any] | None = None,
        json_body: Any = None,
        **kwargs,
    ) -> Union[Awaitable[ParsedResponse[ResponseType]], ParsedResponse[ResponseType]]:
        """Calls the endpoint on the API with given path_params, query_params
        and json_body. URL is resolved from api and endpoint and all path
        params required by the endpoint must be defined in path_params
        dictionary. All the path_params and query_params will be converted to
        string. Parses the response to response_class type and returns a
        ParsedResponse or it's awaitable.

        @param api:
        @param endpoint:
        @param path_params:
        @param query_param:
        @param json_body:
        @return:
        """
        url = api.get_prefix() + endpoint.path
        missed_params = endpoint.path_params - (path_params.keys() if path_params else set())
        if missed_params:
            raise ValueError(f"missed params {missed_params}")
        if path_params:
            url = url.format(**path_params)

        # Handling query params with empty values
        # since fastapi does not support optional query params with empty values
        if query_param:
            query_param = {k: v for k, v in query_param.items() if v is not None}

        try:
            if self._auth_client:
                auth_headers = [self._auth_client.get_authorization_header(**kwargs)]
            else:
                auth_headers = None
            return self._do_request(
                url=url,
                endpoint=endpoint,
                query_param=query_param,
                json_body=json_body,
                headers=auth_headers,
                **kwargs,
            )
        except httpx.HTTPStatusError as exc:
            # retry once if API was unauthorized, potentially due to token expiration
            if exc.response.status_code == httpx.codes.UNAUTHORIZED:
                auth_headers = [self._auth_client.get_authorization_header(**kwargs)]
                return self._do_request(
                    url=url,
                    endpoint=endpoint,
                    query_param=query_param,
                    json_body=json_body,
                    headers=auth_headers,
                )
            if (
                exc.response.status_code == httpx.codes.BAD_GATEWAY
                or exc.response.status_code == httpx.codes.GATEWAY_TIMEOUT
            ):
                raise GatewayTimeoutException
            raise

    @abc.abstractmethod
    def _do_request(
        self,
        url: str,
        endpoint: EndPoint[ResponseType],
        query_param: dict[Any, Any] | None = None,
        json_body: Any = None,
        headers: Sequence[tuple[str, str]] | None = None,
        **kwargs,
    ) -> Union[Awaitable[ParsedResponse[ResponseType]], ParsedResponse[ResponseType]]:
        raise NotImplementedError()

    @staticmethod
    def _handle_response(
        endpoint: EndPoint[Any], response: httpx.Response
    ) -> ParsedResponse[ResponseType]:
        response.raise_for_status()
        content = None
        if endpoint.response_parser:
            content = endpoint.response_parser.parse(response)
        return ParsedResponse(content=content, raw_response=response)


class AriesApiClient(AriesAbstractApiClient):
    def __init__(self, host: str, auth_client: AbstractAuthClient | None = None):
        super(AriesApiClient, self).__init__(auth_client=auth_client)
        self._httpx_client = httpx.Client(
            base_url=host,
        )

    def _do_request(
        self,
        url: str,
        endpoint: EndPoint[ResponseType],
        query_param: dict[Any, Any] | None = None,
        json_body: Any = None,
        headers: Sequence[tuple[str, str]] | None = None,
        **kwargs,
    ) -> ParsedResponse[ResponseType]:
        if self._httpx_client.is_closed:
            raise ValueError("API client was closed, please create a new api client")
        response = self._httpx_client.request(
            method=endpoint.http_verb,
            url=url,
            params=query_param,
            json=json_body,
            headers=headers,
            timeout=kwargs.get("timeout", 30),
        )

        return self._handle_response(endpoint=endpoint, response=response)

    def close(self):
        self._httpx_client.close()


class AsyncAriesApiClient(AriesAbstractApiClient):
    def __init__(self, host: str, auth_client: AbstractAuthClient | None = None):
        super(AsyncAriesApiClient, self).__init__(auth_client=auth_client)
        self._httpx_client = httpx.AsyncClient(
            base_url=host,
        )

    async def close(self):
        await self._httpx_client.aclose()

    def _do_request(
        self,
        url: str,
        endpoint: EndPoint[ResponseType],
        query_param: dict[Any, Any] | None = None,
        json_body: Any = None,
        headers: Sequence[tuple[str, str]] | None = None,
        **kwargs,
    ) -> Awaitable[ParsedResponse[ResponseType]]:
        return asyncio.create_task(
            self._do_async_request(
                url=url,
                endpoint=endpoint,
                query_param=query_param,
                json_body=json_body,
                headers=headers,
                **kwargs,
            )
        )

    async def _do_async_request(
        self,
        url: str,
        endpoint: EndPoint[ResponseType],
        query_param: dict[Any, Any] | None = None,
        json_body: Any = None,
        headers: Sequence[tuple[str, str]] | None = None,
        **kwargs,
    ) -> [ParsedResponse[ResponseType]]:  # type: ignore
        if self._httpx_client.is_closed:
            raise ValueError("API client was closed, please create a new api client")
        response = await self._httpx_client.request(
            method=endpoint.http_verb,
            url=url,
            params=query_param,
            json=json_body,
            headers=headers,
            timeout=kwargs.get("timeout", 30),
        )

        return self._handle_response(endpoint=endpoint, response=response)
