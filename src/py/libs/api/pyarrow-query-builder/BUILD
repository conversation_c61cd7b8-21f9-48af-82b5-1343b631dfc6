python_sources(
    sources=["pyarrow_query_builder/**/*.py"],
    dependencies=[
        "//:se_libs#se-schema",
        "//:se_libs#flang",
        "//:3rdparty#elasticsearch-dsl",
        "//:3rdparty#pandas",
    ],
)

python_tests(
    name="tests",
    sources=["test_pyarrow_query_builder/**/test_*.py"],
    dependencies=[":pa_flang_test_resources"],
)

resources(
    name="pa_flang_test_resources",
    sources=["test_pyarrow_query_builder/**/*.parquet"],
)
