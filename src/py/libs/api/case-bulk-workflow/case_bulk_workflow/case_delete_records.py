# type: ignore
import logging
from .base import BaseTask
from api_sdk.auth import Tenancy
from api_sdk.es_dsl.base import TermFilter
from api_sdk.exceptions import NotFound
from api_sdk.repository.syncronous.request_bound import BaseRepository
from case_bulk_workflow.base import CaseAttachSpec
from case_bulk_workflow.schemas.case_bulk_in import CaseBulkOutput
from case_bulk_workflow.schemas.cases import (
    Case,
    CaseCall,
    CaseEmail,
    CaseMeeting,
    CaseMessage,
    CaseOrder,
    CaseRecordSearch,
    CaseText,
)
from case_bulk_workflow.schemas.common import model_map
from collections import defaultdict
from se_db_utils.database import Database
from se_elastic_schema.static.surveillance import AlertHitStatus

log = logging.getLogger("api-worker")
MIN_ITEMS_FOR_SCROLL = 250


CASE_COMMS_MODELS = [CaseCall, CaseEmail, CaseMeeting, CaseMessage, CaseText]
CASE_ORDER_MODELS = [CaseOrder]


class CaseDeleteRecordsByIdsTask(BaseTask):
    """If case records are deleted from a case, then workflow field of alerts
    linked to the case records needs to be updated."""

    def __init__(
        self,
        tenancy: Tenancy,
        record_handler: BaseRepository,
        db: Database,
        owner=None,
    ):
        super().__init__(tenancy, record_handler, owner=owner, db=db)

        self.deletable_models = []
        if self.has_tsurv:
            self.deletable_models.extend(CASE_ORDER_MODELS)
        if self.has_csurv:
            self.deletable_models.extend(CASE_COMMS_MODELS)

    def run(self, bulk_request):
        case_id = bulk_request.case_id
        records_in = bulk_request.records
        error_count = 0

        log.info("Fetching the deletable records with the ids from RecordIn")
        task_spec = CaseAttachSpec(records_in=records_in, keep_back_links=True)
        task_spec.data = self.get_records(task_spec, models=self.deletable_models)
        case_records = defaultdict(list)

        if task_spec.data is None:
            log.error("No records were found matching supplied RecordIn")
            raise NotFound(message="No records were found matching supplied RecordIn")

        hit_ids = []
        for data in task_spec.data:
            data = data["_source"]
            if data is None:
                error_count += 1
                continue
            try:
                model = model_map.get(data["&model"])
                rec = model(**data)
                case_records[model].append(rec.to_dict(strip_meta=True))
                log.info("Deleting record %s (%s) to case %s", rec.id_, rec.model_, case_id)
                self.case_record_repo.s_delete_existing(rec)
                hit_ids.append(rec.original_record_id)

            except Exception as e:
                log.debug("Exception: %s", e)
                error_count += 1

        alert_records = {}
        if hit_ids:
            alert_filters = [
                TermFilter(name="workflow.caseSlug", value=case_id).to_dict(meta_fields={}),
                TermFilter(name="hit.&id", value=hit_ids).to_dict(meta_fields={}),
            ]

            workflow = {
                "status": AlertHitStatus.UNRESOLVED,
                "caseId": None,
                "caseSlug": None,
            }

            # Detaching alerts from case, as case record is deleted
            alert_records = self.get_back_link_records(
                filters=alert_filters,
                workflow=workflow,
                task_spec=CaseAttachSpec(
                    records_in=[{"alert_id": id_, "id_": id_} for id_ in hit_ids]
                ),
                all_alert_models=True,
            )

        return CaseBulkOutput(alert_records=alert_records, case_records=case_records)


class CaseDeleteRecordsByFilterTask(BaseTask):
    def __init__(
        self,
        tenancy: Tenancy,
        record_handler: BaseRepository,
        db: Database,
    ):
        super().__init__(tenancy, record_handler, db=db)

    def run(self, bulk_request):
        case_id = bulk_request.case_id
        filter_def = bulk_request.filter

        results = self.scan_records(
            index=self.case_record_repo.index_for_record_model(Case),
            search_model=CaseRecordSearch(
                case_id=case_id, **filter_def.to_params().as_search_kwargs()
            ),
        )

        case_records = defaultdict(list)
        for record in results:
            try:
                log.info("Fetching record %s (%s)", record.id_, record.model_)
                model = model_map.get(record.model_)
                rec = model.from_case_record(record)
                case_records[model].append(rec.to_dict(strip_meta=True))

                log.info("Deleting record %s (%s) to case %s", rec.id_, rec.model_, case_id)
                self.case_record_repo.s_delete_existing(rec)

            except Exception as e:
                log.error("Failed to delete record from case %s", case_id)
                log.debug("Exception: %s", e)

        return CaseBulkOutput(case_records=case_records)
