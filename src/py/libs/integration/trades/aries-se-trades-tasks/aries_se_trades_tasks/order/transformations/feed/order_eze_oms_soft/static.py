from aries_se_core_tasks.utilities.data_utils import BaseColumns
from se_enums.core import BaseStrEnum

UNIQUE_IDENTIFIER_REGEX = r"(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(\d{2})"
ORDER_EZE_OMS_SOFT_ALLOCATIONS = "order_eze_oms_soft_allocations"
ORDER_EZE_OMS_SOFT_EXECUTIONS = "order_eze_oms_soft_executions"
ORDER_EZE_OMS_SOFT_ORDERS = "order_eze_oms_soft_orders"

ORDER_EZE_OMS_SOFT_WORKFLOW_NAME = "order_eze_oms_soft"


class Prefix:
    ALLOCATIONS = "ALLOC"
    EXECUTIONS = "EXE"
    ORDERS = "ORDERS"


class EzeFileType:
    ORDERS = "orders"
    EXECUTIONS = "executions"
    ALLOCATIONS = "allocations"


class EzeSoftOmsAllocationsSourceColumns(BaseColumns):
    ACCOUNT = "ALLOC_ACCOUNT"
    ALLOCATION_DATE = "ALLOC_ALLOCATION_DATE"
    ALLOCATION_ID = "ALLOC_ALLOCATION_ID"
    AMOUNT = "ALLOC_AMOUNT"
    BROKER = "ALLOC_BROKER"
    CUSTODIAN = "ALLOC_CUSTODIAN"
    FX_RATE = "ALLOC_FX_RATE"
    INTERFACE_ID = "ALLOC_INTERFACE_ID"
    ISIN = "ALLOC_ISIN"
    NET_MONEY = "ALLOC_NET_MONEY"
    ORDER_ID = "ALLOC_ORDER_ID"
    PRICE = "ALLOC_PRICE"
    SECURITY_CURRENCY = "ALLOC_SECURITY_CURRENCY"
    SECURITY_TYPE = "ALLOC_SECURITY_TYPE"
    SIDE = "ALLOC_SIDE"
    STATE = "ALLOC_STATE"
    STATUS = "ALLOC_STATUS"
    STRATEGY = "ALLOC_STRATEGY"
    SYMBOL = "ALLOC_SYMBOL"
    TOTAL_COMMISSION = "ALLOC_TOTAL_COMMISSION"
    TOTAL_FEES = "ALLOC_TOTAL_FEES"
    TRADE_ID = "ALLOC_TRADE_ID"

    @classmethod
    def required_columns_from_allocations_for_final_order_df(cls):
        return [
            cls.ORDER_ID,
            cls.ACCOUNT,
        ]


class EzeSoftOmsOrdersSourceColumns(BaseColumns):
    ALGO = "ORDERS_ALGO"
    BROKER = "ORDERS_BROKER"
    COMM_RATE = "ORDERS_COMM_RATE"
    COUNTRY = "ORDERS_COUNTRY"
    CUSIP = "ORDERS_CUSIP"
    FX_RATE = "ORDERS_FX_RATE"
    INTERFACE_ID = "ORDERS_INTERFACE_ID"
    ISIN = "ORDERS_ISIN"
    LAST_EXECUTION = "ORDERS_LAST_EXECUTION"
    MANAGER = "ORDERS_MANAGER"
    MANAGER_LIMIT = "ORDERS_MANAGER_LIMIT"
    ORDER_DATE = "ORDERS_ORDER_DATE"
    ORDER_ID = "ORDERS_ORDER_ID"
    ORDER_TYPE = "ORDERS_ORDER_TYPE"
    ORIGINAL_TIMESTAMP = "ORDERS_ORIGINAL_TIMESTAMP"
    ORIGINAL_TRADE_ID = "ORDERS_ORIGINAL_TRADE_ID"
    PRICE = "ORDERS_PRICE"
    QUANTITY = "ORDERS_QUANTITY"
    SECURITY_CURRENCY = "ORDERS_SECURITY_CURRENCY"
    SECURITY_TYPE = "ORDERS_SECURITY_TYPE"
    SEDOL = "ORDERS_SEDOL"
    SETTLE_DATE = "ORDERS_SETTLE_DATE"
    SIDE = "ORDERS_SIDE"
    STATE = "ORDERS_STATE"
    STATUS = "ORDERS_STATUS"
    SYMBOL = "ORDERS_SYMBOL"
    TOTAL_FEES = "ORDERS_TOTAL_FEES"
    TRADE_DATE = "ORDERS_TRADE_DATE"
    TRADE_ID = "ORDERS_TRADE_ID"
    TRADER_ID = "ORDERS_TRADER_ID"
    USERDEFINED_1 = "ORDERS_USERDEFINED_1"
    USERDEFINED_2 = "ORDERS_USERDEFINED_2"


class EzeSoftOmsExecutionsSourceColumns(BaseColumns):
    AMOUNT = "EXE_AMOUNT"
    BROKER = "EXE_BROKER"
    EXECUTION_ID = "EXE_EXECUTION_ID"
    EXECUTION_TIME = "EXE_EXECUTION_TIME"
    EXCHANGE = "EXE_EXCHANGE"
    EXECUTING_EXCHANGE = "EXE_EXECUTING_EXCHANGE"
    FX_RATE = "EXE_FX_RATE"
    INTERFACE_ID = "EXE_INTERFACE_ID"
    ISIN = "EXE_ISIN"
    LAST_CAPACITY = "EXE_LAST_CAPACITY"
    LIQUIDITY_INDICATOR = "EXE_LIQUIDITY_INDICATOR"
    MANAGER = "EXE_MANAGER"
    ORDER_ID = "EXE_ORDER_ID"
    PRICE = "EXE_PRICE"
    SECURITY_CURRENCY = "EXE_SECURITY_CURRENCY"
    SECURITY_TYPE = "EXE_SECURITY_TYPE"
    SETTLE_CURRENCY = "EXE_SETTLE_CURRENCY"
    SETTLE_DATE = "EXE_SETTLE_DATE"
    SIDE = "EXE_SIDE"
    STATUS = "EXE_STATUS"
    SYMBOL = "EXE_SYMBOL"
    TRADE_DATE = "EXE_TRADE_DATE"
    TRADE_ID = "EXE_TRADE_ID"
    TRADER = "EXE_TRADER"


EZE_ORDER_SCHEMA = {
    EzeSoftOmsOrdersSourceColumns.ALGO: "string",
    EzeSoftOmsOrdersSourceColumns.BROKER: "string",
    EzeSoftOmsOrdersSourceColumns.COMM_RATE: "string",
    EzeSoftOmsOrdersSourceColumns.COUNTRY: "string",
    EzeSoftOmsOrdersSourceColumns.CUSIP: "string",
    EzeSoftOmsOrdersSourceColumns.FX_RATE: "string",
    EzeSoftOmsOrdersSourceColumns.INTERFACE_ID: "string",
    EzeSoftOmsOrdersSourceColumns.ISIN: "string",
    EzeSoftOmsOrdersSourceColumns.LAST_EXECUTION: "string",
    EzeSoftOmsOrdersSourceColumns.MANAGER: "string",
    EzeSoftOmsOrdersSourceColumns.MANAGER_LIMIT: "string",
    EzeSoftOmsOrdersSourceColumns.ORDER_DATE: "string",
    EzeSoftOmsOrdersSourceColumns.ORDER_ID: "string",
    EzeSoftOmsOrdersSourceColumns.ORDER_TYPE: "string",
    EzeSoftOmsOrdersSourceColumns.ORIGINAL_TIMESTAMP: "string",
    EzeSoftOmsOrdersSourceColumns.ORIGINAL_TRADE_ID: "string",
    EzeSoftOmsOrdersSourceColumns.PRICE: "string",
    EzeSoftOmsOrdersSourceColumns.QUANTITY: "string",
    EzeSoftOmsOrdersSourceColumns.SECURITY_CURRENCY: "string",
    EzeSoftOmsOrdersSourceColumns.SECURITY_TYPE: "string",
    EzeSoftOmsOrdersSourceColumns.SEDOL: "string",
    EzeSoftOmsOrdersSourceColumns.SETTLE_DATE: "string",
    EzeSoftOmsOrdersSourceColumns.SIDE: "string",
    EzeSoftOmsOrdersSourceColumns.STATE: "string",
    EzeSoftOmsOrdersSourceColumns.STATUS: "string",
    EzeSoftOmsOrdersSourceColumns.SYMBOL: "string",
    EzeSoftOmsOrdersSourceColumns.TOTAL_FEES: "string",
    EzeSoftOmsOrdersSourceColumns.TRADE_DATE: "string",
    EzeSoftOmsOrdersSourceColumns.TRADE_ID: "string",
    EzeSoftOmsOrdersSourceColumns.TRADER_ID: "string",
    EzeSoftOmsOrdersSourceColumns.USERDEFINED_1: "string",
    EzeSoftOmsOrdersSourceColumns.USERDEFINED_2: "string",
}

EZE_EXECUTION_SCHEMA = {
    EzeSoftOmsExecutionsSourceColumns.AMOUNT: "string",
    EzeSoftOmsExecutionsSourceColumns.BROKER: "string",
    EzeSoftOmsExecutionsSourceColumns.EXECUTION_ID: "string",
    EzeSoftOmsExecutionsSourceColumns.EXECUTION_TIME: "string",
    EzeSoftOmsExecutionsSourceColumns.EXCHANGE: "string",
    EzeSoftOmsExecutionsSourceColumns.EXECUTING_EXCHANGE: "string",
    EzeSoftOmsExecutionsSourceColumns.FX_RATE: "string",
    EzeSoftOmsExecutionsSourceColumns.INTERFACE_ID: "string",
    EzeSoftOmsExecutionsSourceColumns.ISIN: "string",
    EzeSoftOmsExecutionsSourceColumns.LAST_CAPACITY: "string",
    EzeSoftOmsExecutionsSourceColumns.LIQUIDITY_INDICATOR: "string",
    EzeSoftOmsExecutionsSourceColumns.MANAGER: "string",
    EzeSoftOmsExecutionsSourceColumns.ORDER_ID: "string",
    EzeSoftOmsExecutionsSourceColumns.PRICE: "string",
    EzeSoftOmsExecutionsSourceColumns.SECURITY_CURRENCY: "string",
    EzeSoftOmsExecutionsSourceColumns.SECURITY_TYPE: "string",
    EzeSoftOmsExecutionsSourceColumns.SETTLE_CURRENCY: "string",
    EzeSoftOmsExecutionsSourceColumns.SETTLE_DATE: "string",
    EzeSoftOmsExecutionsSourceColumns.SIDE: "string",
    EzeSoftOmsExecutionsSourceColumns.STATUS: "string",
    EzeSoftOmsExecutionsSourceColumns.SYMBOL: "string",
    EzeSoftOmsExecutionsSourceColumns.TRADE_DATE: "string",
    EzeSoftOmsExecutionsSourceColumns.TRADE_ID: "string",
    EzeSoftOmsExecutionsSourceColumns.TRADER: "string",
}

EZE_ALLOCATION_SCHEMA = {
    EzeSoftOmsAllocationsSourceColumns.ACCOUNT: "string",
    EzeSoftOmsAllocationsSourceColumns.ALLOCATION_DATE: "string",
    EzeSoftOmsAllocationsSourceColumns.ALLOCATION_ID: "string",
    EzeSoftOmsAllocationsSourceColumns.AMOUNT: "string",
    EzeSoftOmsAllocationsSourceColumns.BROKER: "string",
    EzeSoftOmsAllocationsSourceColumns.CUSTODIAN: "string",
    EzeSoftOmsAllocationsSourceColumns.FX_RATE: "string",
    EzeSoftOmsAllocationsSourceColumns.INTERFACE_ID: "string",
    EzeSoftOmsAllocationsSourceColumns.ISIN: "string",
    EzeSoftOmsAllocationsSourceColumns.NET_MONEY: "string",
    EzeSoftOmsAllocationsSourceColumns.ORDER_ID: "string",
    EzeSoftOmsAllocationsSourceColumns.PRICE: "string",
    EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY: "string",
    EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE: "string",
    EzeSoftOmsAllocationsSourceColumns.SIDE: "string",
    EzeSoftOmsAllocationsSourceColumns.STATE: "string",
    EzeSoftOmsAllocationsSourceColumns.STATUS: "string",
    EzeSoftOmsAllocationsSourceColumns.STRATEGY: "string",
    EzeSoftOmsAllocationsSourceColumns.SYMBOL: "string",
    EzeSoftOmsAllocationsSourceColumns.TOTAL_COMMISSION: "string",
    EzeSoftOmsAllocationsSourceColumns.TOTAL_FEES: "string",
    EzeSoftOmsAllocationsSourceColumns.TRADE_ID: "string",
}


class EzeOMSEnum(BaseStrEnum):
    SHORT = "SHORT"
    MARKET = "MARKET"
    ALLOCATION = "ALLOCATION"
    LIMIT = "LIMIT"
    STOP = "STOP"
    DONE = "DONE"
    CURRENCY = "CURRENCY"
    XOFF = "XOFF"


class EzeSoftOmsTempColumns:
    ASSET_CLASS = "__asset_class__"
    BUY_SELL = "__buy_sell__"
    EXPIRY_DATE = "__expiry_date__"
    EXPIRY_DATE_CONVERTED = "__expiry_date_converted__"
    OPTION_TYPE = "__option_type__"
    STRIKE_PRICE = "__strike_price__"
    UNDERLYING_SYMBOL = "__underlying_symbol__"
    UNDERLYING_SYMBOL_EXPIRY_CODE = "__underlying_symbol_expiry_code__"
    VENUE = "__venue__"
    ISIN = "__isin__"
    SYMBOL = "__symbol__"
    ULTIMATE_VENUE = "__ultimate_venue__"
    TRADER = "__trader__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
