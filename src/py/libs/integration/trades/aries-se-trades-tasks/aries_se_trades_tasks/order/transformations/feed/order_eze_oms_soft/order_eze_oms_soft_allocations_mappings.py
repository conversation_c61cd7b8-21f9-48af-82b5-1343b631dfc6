# mypy: disable-error-code="attr-defined,no-any-return,empty-body"
import pandas as pd
from aries_se_core_tasks.currency.convert_minor_to_major import Params as ParamsConvertMinorToMajor
from aries_se_core_tasks.currency.convert_minor_to_major import run_convert_minor_to_major
from aries_se_core_tasks.datetime.convert_datetime import (
    run_convert_datetime,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (
    Params as ConcatAttributesParams,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined]
    run_concat_attributes,
)
from aries_se_core_tasks.transform.map.map_conditional import Params as MapConditionalParams
from aries_se_core_tasks.transform.map.map_conditional import run_map_conditional
from aries_se_core_tasks.transform.map.map_value import Params as ParamsMapValue
from aries_se_core_tasks.transform.map.map_value import run_map_value
from aries_se_core_tasks.utilities.static import Delimiters
from aries_se_trades_tasks.instrument.instrument_identifiers import run_instrument_identifiers
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.static import (
    EzeOMSEnum,
    EzeSoftOmsAllocationsSourceColumns,
    EzeSoftOmsExecutionsSourceColumns,
    EzeSoftOmsOrdersSourceColumns,
    EzeSoftOmsTempColumns,
)
from aries_se_trades_tasks.orders_and_tr.identifiers.merge_market_identifiers import (
    run_merge_market_identifiers,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ParamsConvertDatetime
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.models import Order
from se_elastic_schema.static.mifid2 import (
    BuySellIndicator,
    OrderStatus,
    PriceNotation,
    QuantityNotation,
    ShortSellingIndicator,
)
from se_trades_tasks.abstractions.abstract_order_transformations import AbstractOrderTransformations
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    Params as ParamsPartyIdentifiers,
)
from se_trades_tasks.order.party.generic_order_party_identifiers import (
    run_generic_order_party_identifiers,
)
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, OrderTempColumns, add_prefix
from se_trades_tasks.order.transformations.universal.static import TempColumns
from se_trades_tasks.order_and_tr.instrument.identifiers.identifiers import (
    Params as ParamsInstrumentIdentifiers,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.merge_market_identifiers import (
    Params as ParamsMergeMarketIdentifiers,
)
from se_trades_tasks.order_and_tr.instrument.identifiers.symbol_and_expiry_code.utils import (
    underlying_symbol_and_expiry_code_replacements,
)
from se_trades_tasks.order_and_tr.static import AssetClass, PartyPrefix


class AllocationsOrderEzeOmsSoftMappings(AbstractOrderTransformations):  # type:ignore[misc]
    def __init__(self, es_client, tenant, realm, source_file_uri: str, **kwargs):
        super().__init__(**kwargs)
        self.es_client = es_client
        self.tenant = tenant
        self.realm = realm
        self.file_uri = source_file_uri

        self.pre_process_df: pd.DataFrame

    def process(self) -> pd.DataFrame:
        # Preprocessing
        self.pre_process()

        # SteelEye utility mappings
        self.meta_model()
        self.data_source_name()
        self.source_key()
        self.source_index()
        self.is_synthetic()

        # Dates
        self.timestamps_order_received()
        self.timestamps_order_submitted()
        self.transaction_details_trading_date_time()
        self.timestamps_trading_date_time()
        self.date()
        self.timestamps_order_status_updated()

        # Identifiers
        self.order_identifiers_aggregated_order_id_code()
        self.order_identifiers_order_id_code()
        self.id()
        self.order_identifiers_transaction_ref_no()
        self.report_details_transaction_ref_no()
        self.order_identifiers_parent_order_id()

        # Order Details
        self.execution_details_order_status()
        self.execution_details_outgoing_order_addl_info()
        self.buy_sell()
        self.execution_details_buy_sell_indicator()
        self.execution_details_short_selling_indicator()

        # Price
        self.transaction_details_price_currency()
        self.transaction_details_commission_amount()
        self.transaction_details_commission_amount_currency()
        self.transaction_details_price_notation()

        # Quantities
        self.price_forming_data_initial_quantity()
        self.price_forming_data_remaining_quantity()
        self.price_forming_data_traded_quantity()
        self.transaction_details_quantity()
        self.transaction_details_quantity_notation()

        # Trade details
        self.transaction_details_ultimate_venue()
        self.transaction_details_venue()
        self.transaction_details_record_type()
        self.transaction_details_buy_sell_indicator()

        # Instrument and Party mappings
        self.market_identifiers_instrument()
        self.market_identifiers_parties()
        self.market_identifiers()

        # Postprocessing
        self.post_process()
        return self.target_df

    def get_venue(self):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.VENUE] = "XOFF"

    def get_ultimate_venue(self):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.ULTIMATE_VENUE] = "XOFF"

    def get_isin(self):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.ISIN] = self.source_frame.loc[
            :, [EzeSoftOmsAllocationsSourceColumns.ISIN]
        ]

    def get_newo_in_file(self):
        self.pre_process_df.loc[:, OrderTempColumns.NEWO_IN_FILE] = pd.DataFrame(
            data=[True] * self.pre_process_df.shape[0],
            index=self.pre_process_df.index,
            columns=[OrderTempColumns.NEWO_IN_FILE],
        )

    def get_asset_class(self):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.ASSET_CLASS] = pd.NA
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.ASSET_CLASS] = run_map_conditional(
            source_frame=self.source_frame.loc[
                :, [EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE]
            ],
            params=MapConditionalParams(
                target_attribute=EzeSoftOmsTempColumns.ASSET_CLASS,
                cases=[
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('STOCK', case=False, na=False)",  # noqa: E501
                        value=AssetClass.EQUITY,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('FORWARD', case=False, na=False)",  # noqa: E501
                        value=AssetClass.FX_FORWARD,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('CURRENCY', case=False, na=False)",  # noqa: E501
                        value=AssetClass.FX_SPOT,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.isin(['CALL', 'PUT'])",  # noqa: E501
                        value=AssetClass.OPTION,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('FUTURE', case=False, na=False)",  # noqa: E501
                        value=AssetClass.FUTURE,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.isin(['SWAP', 'EQUITYSWAP'])",  # noqa: E501
                        value=AssetClass.EQUITY_SWAP,
                    ),
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('CFD', case=False, na=False)",  # noqa: E501
                        value=AssetClass.CFD,
                    ),
                ],
            ),
            skip_serializer=True,
        )

    def get_underlying_symbol_expiry_code(self, options_mask: pd.Series):
        symbol_length_mask = (
            self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.SYMBOL]
            .astype("string")
            .str.len()
            <= 5
        )

        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE] = pd.NA
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL] = pd.NA

        self.pre_process_df.loc[
            symbol_length_mask, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE
        ] = self.source_frame.loc[symbol_length_mask, EzeSoftOmsAllocationsSourceColumns.SYMBOL]

        if (~symbol_length_mask).any():
            temp_df = pd.DataFrame(index=self.source_frame.loc[~symbol_length_mask].index)
            temp_df[EzeSoftOmsTempColumns.SYMBOL] = self.source_frame.loc[
                ~symbol_length_mask, EzeSoftOmsAllocationsSourceColumns.SYMBOL
            ]
            temp_df[EzeSoftOmsTempColumns.VENUE] = self.pre_process_df.loc[
                ~symbol_length_mask, EzeSoftOmsTempColumns.VENUE
            ]
            temp_df[EzeSoftOmsTempColumns.ASSET_CLASS] = self.pre_process_df.loc[
                ~symbol_length_mask, EzeSoftOmsTempColumns.ASSET_CLASS
            ]
            temp_df.loc[options_mask, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL] = (
                temp_df.loc[options_mask, EzeSoftOmsTempColumns.SYMBOL].astype("string").str[:-2]
            )
            temp_df.loc[~options_mask, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL] = (
                temp_df.loc[~options_mask, EzeSoftOmsTempColumns.SYMBOL]
                .astype("string")
                .str.split(" ")
                .str[0]
            )

            result: pd.DataFrame = underlying_symbol_and_expiry_code_replacements(
                dataframe=temp_df,
                symbol_column=EzeSoftOmsTempColumns.SYMBOL,
                asset_class_column=TempColumns.ASSET_CLASS,
                venue_column=EzeSoftOmsTempColumns.VENUE,
                symbol_and_expiry_code_target_column=EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE,
                symbol_only_target_column=EzeSoftOmsTempColumns.UNDERLYING_SYMBOL,
            )

            self.pre_process_df.loc[
                ~symbol_length_mask, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE
            ] = result.loc[:, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE]
            self.pre_process_df.loc[
                ~symbol_length_mask, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL
            ] = result.loc[:, EzeSoftOmsTempColumns.UNDERLYING_SYMBOL]

    def get_option_strike_price(self, options_mask: pd.Series):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.STRIKE_PRICE] = pd.NA

        if options_mask.any():
            option_symbols = self.source_frame.loc[
                options_mask, EzeSoftOmsAllocationsSourceColumns.SYMBOL
            ].astype("string")

            # Find the last occurrence of 'C' or 'P' and extract everything after it
            strike_prices = option_symbols.apply(self._extract_strike_price_from_symbol)

            self.pre_process_df.loc[options_mask, EzeSoftOmsTempColumns.STRIKE_PRICE] = (
                pd.to_numeric(strike_prices, errors="coerce")
            )

    def _extract_strike_price_from_symbol(self, symbol: str) -> str:
        if pd.isna(symbol) or not isinstance(symbol, str):
            return pd.NA

        last_c_pos = symbol.rfind("C")
        last_p_pos = symbol.rfind("P")

        last_pos = max(last_c_pos, last_p_pos)

        if last_pos == -1:
            return pd.NA

        strike_price = symbol[last_pos + 1 :]

        return strike_price.strip() if strike_price else pd.NA

    def get_expiry_date(self, options_mask: pd.Series):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.EXPIRY_DATE] = pd.NA
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.EXPIRY_DATE_CONVERTED] = pd.NA

        options_expiry_raw = (
            self.source_frame.loc[options_mask, EzeSoftOmsAllocationsSourceColumns.SYMBOL]
            .str.split(" ")
            .str[1]  # Take the second value (index 1)
        )

        # Convert mm/dd/yy to YYYY-MM-DD format
        self.pre_process_df.loc[options_mask, EzeSoftOmsTempColumns.EXPIRY_DATE_CONVERTED] = (
            pd.to_datetime(options_expiry_raw, format="%m/%d/%y", errors="coerce")
        )

        non_options_mask = ~options_mask

        month_map = {
            "F": "01",  # January
            "G": "02",  # February
            "H": "03",  # March
            "J": "04",  # April
            "K": "05",  # May
            "M": "06",  # June
            "N": "07",  # July
            "Q": "08",  # August
            "U": "09",  # September
            "V": "10",  # October
            "X": "11",  # November
            "Z": "12",  # December
        }

        # Extract last two characters for futures
        futures_code = (
            self.source_frame.loc[non_options_mask, EzeSoftOmsAllocationsSourceColumns.SYMBOL]
            .astype("string")
            .str[-2:]
        )

        # Parse month and year from futures code
        futures_month = futures_code.str[0].map(month_map)
        futures_year = "202" + futures_code.str[1]  # Assuming 2020s decade

        # Create YYYY-MM format for futures
        futures_expiry = futures_year + "-" + futures_month

        # Convert to datetime (first day of the month)
        self.pre_process_df.loc[non_options_mask, EzeSoftOmsTempColumns.EXPIRY_DATE_CONVERTED] = (
            pd.to_datetime(futures_expiry + "-01", format="%Y-%m-%d", errors="coerce")
        )

    def get_option_type(self, options_mask: pd.Series):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.OPTION_TYPE] = pd.NA
        self.pre_process_df.loc[options_mask, EzeSoftOmsTempColumns.OPTION_TYPE] = (
            run_map_conditional(
                source_frame=self.source_frame.loc[
                    options_mask, [EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE]
                ],
                params=MapConditionalParams(
                    target_attribute=EzeSoftOmsTempColumns.OPTION_TYPE,
                    cases=[
                        Case(
                            query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('PUT', case=False, na=False)",  # noqa: E501
                            value="PUTO",
                        ),
                        Case(
                            query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('CALL', case=False, na=False)",  # noqa: E501
                            value="CALL",
                        ),
                        Case(
                            query=f"~(`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('CALL', case=False, na=False)) &"  # noqa: E501
                            f"~(`{EzeSoftOmsAllocationsSourceColumns.SECURITY_TYPE}`.str.fullmatch('PUT', case=False, na=False))",  # noqa: E501
                            value=pd.NA,
                        ),
                    ],
                ),
                skip_serializer=True,
            )
        )

    def get_execution_within_firm(self):
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.EXECUTION_WITHIN_FIRM] = pd.NA
        self.pre_process_df.loc[:, EzeSoftOmsTempColumns.EXECUTION_WITHIN_FIRM] = (
            self.source_frame.loc[:, EzeSoftOmsExecutionsSourceColumns.TRADER]
        )

    def _pre_process(self):
        self.get_newo_in_file()
        self.get_isin()
        self.get_asset_class()
        self.get_ultimate_venue()
        self.get_venue()
        options_mask = (
            self.pre_process_df.loc[:, TempColumns.ASSET_CLASS]
            .astype("string")
            .str.fullmatch(AssetClass.OPTION, case=False, na=False)
        )
        self.get_option_strike_price(options_mask)
        self.get_underlying_symbol_expiry_code(options_mask)
        self.get_expiry_date(options_mask)
        self.get_option_type(options_mask)
        self.get_execution_within_firm()

    # --- SteelEye utility mappings --- #
    def _meta_model(self) -> pd.DataFrame:
        """__meta_model__ is required downstream for the AssignMetaParent
        task."""

        order_model_name = Order.get_reference().name

        return pd.concat(
            [
                pd.DataFrame(
                    data=order_model_name,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.META_MODEL)],
                ),
                pd.DataFrame(
                    data=order_model_name,
                    index=self.source_frame.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.META_MODEL)],
                ),
            ],
            axis=1,
        )

    def _data_source_name(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="Order Eze Soft OMS - Allocations",
            index=self.source_frame.index,
            columns=[OrderColumns.DATA_SOURCE_NAME],
        )

    def _source_key(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.file_uri,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_KEY],
        )

    def _source_index(self):
        return pd.DataFrame(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            columns=[OrderColumns.SOURCE_INDEX],
        )

    def _is_synthetic(self) -> pd.DataFrame:
        result = pd.DataFrame(
            data=[False] * self.target_df.shape[0],
            index=self.target_df.index,
            columns=[add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.IS_SYNTHETIC)],
        )
        result.loc[
            ~self.pre_process_df.loc[:, OrderTempColumns.NEWO_IN_FILE],
            add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.IS_SYNTHETIC),
        ] = True
        return result

    def _timestamps_order_received(self) -> pd.DataFrame:
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=EzeSoftOmsAllocationsSourceColumns.ALLOCATION_DATE,
                target_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )

    def _timestamps_order_submitted(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[OrderColumns.TIMESTAMPS_ORDER_SUBMITTED],
        )

    def _transaction_details_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, EzeSoftOmsAllocationsSourceColumns.ALLOCATION_DATE
            ].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME,
                )
            ],
        )

    def _timestamps_trading_date_time(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_TRADING_DATE_TIME,
                )
            ],
        )

    def _date(self) -> pd.DataFrame:
        return run_convert_datetime(
            source_frame=self.target_df,
            params=ParamsConvertDatetime(
                source_attribute=OrderColumns.TIMESTAMPS_ORDER_RECEIVED,
                source_attribute_format=DatetimeFormat.DATE,
                convert_to=ConvertTo.DATE,
                target_attribute=OrderColumns.DATE,
            ),
            skip_serializer=True,
        )

    def _timestamps_order_status_updated(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df.loc[:, OrderColumns.TIMESTAMPS_ORDER_RECEIVED].values,
            index=self.target_df.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TIMESTAMPS_ORDER_STATUS_UPDATED,
                )
            ],
        )

    def timestamps_order_status_updated(self) -> None:
        pass

    def _order_identifiers_aggregated_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsOrdersSourceColumns.TRADE_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID],
        )

    def _order_identifiers_parent_order_id(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsOrdersSourceColumns.ORIGINAL_TRADE_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_PARENT_ORDER_ID],
        )

    def _order_identifiers_order_id_code(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="A_"
            + self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.ORDER_ID].values,
            index=self.source_frame.index,
            columns=[OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE],
        )

    def _id(self) -> pd.DataFrame:
        return pd.concat(
            [
                pd.DataFrame(
                    data=self.target_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE].values,
                    index=self.target_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)],
                ),
                pd.DataFrame(
                    data=self.target_df.loc[:, OrderColumns.ORDER_IDENTIFIERS_ORDER_ID_CODE].values,
                    index=self.target_df.index,
                    columns=[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)],
                ),
            ],
            axis=1,
        )

    def _order_identifiers_transaction_ref_no(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="A_"
            + self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.TRADE_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.ORDER_IDENTIFIERS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _report_details_transaction_ref_no(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="A_"
            + self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.TRADE_ID].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.REPORT_DETAILS_TRANSACTION_REF_NO,
                )
            ],
        )

    def _hierarchy(self) -> pd.DataFrame:
        pass

    def _execution_details_order_status(self) -> pd.DataFrame:
        return pd.concat(
            [
                pd.DataFrame(
                    data=[OrderStatus.NEWO.value] * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
                pd.DataFrame(
                    data=[OrderStatus.FILL.value] * self.source_frame.shape[0],
                    index=self.source_frame.index,
                    columns=[
                        add_prefix(
                            prefix=ModelPrefix.ORDER_STATE,
                            attribute=OrderColumns.EXECUTION_DETAILS_ORDER_STATUS,
                        )
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        temp_df = self.source_frame[
            [
                EzeSoftOmsAllocationsSourceColumns.ALLOCATION_ID,
                EzeSoftOmsAllocationsSourceColumns.NET_MONEY,
                EzeSoftOmsAllocationsSourceColumns.SYMBOL,
                EzeSoftOmsAllocationsSourceColumns.SIDE,
                EzeSoftOmsAllocationsSourceColumns.TOTAL_FEES,
                EzeSoftOmsAllocationsSourceColumns.INTERFACE_ID,
                EzeSoftOmsAllocationsSourceColumns.STRATEGY,
            ]
        ]

        temp_df[EzeSoftOmsAllocationsSourceColumns.ALLOCATION_ID] = (
            "Allocation ID: " + temp_df[EzeSoftOmsAllocationsSourceColumns.ALLOCATION_ID]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.NET_MONEY] = (
            "Net Money: " + temp_df[EzeSoftOmsAllocationsSourceColumns.NET_MONEY]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.SYMBOL] = (
            "Symbol: " + temp_df[EzeSoftOmsAllocationsSourceColumns.SYMBOL]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.SIDE] = (
            "Side: " + temp_df[EzeSoftOmsAllocationsSourceColumns.SIDE]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.TOTAL_FEES] = (
            "Total Fees: " + temp_df[EzeSoftOmsAllocationsSourceColumns.TOTAL_FEES]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.INTERFACE_ID] = (
            "Interface ID: " + temp_df[EzeSoftOmsAllocationsSourceColumns.INTERFACE_ID]
        )
        temp_df[EzeSoftOmsAllocationsSourceColumns.STRATEGY] = (
            "Strategy: " + temp_df[EzeSoftOmsAllocationsSourceColumns.STRATEGY]
        )

        return run_concat_attributes(
            source_frame=temp_df,
            params=ConcatAttributesParams(
                source_attributes=[
                    EzeSoftOmsAllocationsSourceColumns.ALLOCATION_ID,
                    EzeSoftOmsAllocationsSourceColumns.NET_MONEY,
                    EzeSoftOmsAllocationsSourceColumns.SYMBOL,
                    EzeSoftOmsAllocationsSourceColumns.SIDE,
                    EzeSoftOmsAllocationsSourceColumns.TOTAL_FEES,
                    EzeSoftOmsAllocationsSourceColumns.INTERFACE_ID,
                    EzeSoftOmsAllocationsSourceColumns.STRATEGY,
                ],
                delimiter=Delimiters.NEW_LINE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
            ),
            skip_serializer=True,
        )

    def _transaction_details_record_type(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=EzeOMSEnum.ALLOCATION.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_RECORD_TYPE],
        )

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        return run_map_value(
            source_frame=self.source_frame.loc[:, [EzeSoftOmsAllocationsSourceColumns.SIDE]],
            params=ParamsMapValue(
                source_attribute=EzeSoftOmsAllocationsSourceColumns.SIDE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_SHORT_SELLING_INDICATOR,
                case_insensitive=True,
                value_map={
                    "SHORT": ShortSellingIndicator.SESH.value,
                },
            ),
            skip_serializer=True,
        )

    def _buy_sell(self) -> pd.DataFrame:
        result: pd.DataFrame = run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=EzeSoftOmsAllocationsSourceColumns.SIDE,
                target_attribute=EzeSoftOmsTempColumns.BUY_SELL,
                case_insensitive=True,
                value_map={
                    "BUY": "1",
                    "COVER": "1",
                    "SELL": "2",
                    "SHORT": "2",
                },
            ),
            skip_serializer=True,
        )
        return pd.concat(
            [
                pd.DataFrame(
                    data=result[EzeSoftOmsTempColumns.BUY_SELL].values,
                    index=result.index,
                    columns=[add_prefix(prefix=ModelPrefix.ORDER, attribute=OrderColumns.BUY_SELL)],
                ),
                pd.DataFrame(
                    data=result[EzeSoftOmsTempColumns.BUY_SELL].values,
                    index=result.index,
                    columns=[
                        add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=OrderColumns.BUY_SELL)
                    ],
                ),
            ],
            axis=1,
        )

    def _execution_details_buy_sell_indicator(self) -> pd.DataFrame:
        result: pd.DataFrame = run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=EzeSoftOmsAllocationsSourceColumns.SIDE,
                target_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                case_insensitive=True,
                value_map={
                    "BUY": BuySellIndicator.BUYI.value,
                    "COVER": BuySellIndicator.BUYI.value,
                    "SELL": BuySellIndicator.SELL.value,
                    "SHORT": BuySellIndicator.SELL.value,
                },
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_buy_sell_indicator(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.target_df[OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR],
        )

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[
                :, EzeSoftOmsAllocationsSourceColumns.TOTAL_COMMISSION
            ].values,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT],
        )

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_minor_to_major(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_COMMISSION_AMOUNT_CURRENCY,
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_price_currency(self) -> pd.DataFrame:
        result: pd.DataFrame = run_convert_minor_to_major(
            source_frame=self.source_frame,
            params=ParamsConvertMinorToMajor(
                source_ccy_attribute=EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY,
                target_ccy_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_price_notation(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=PriceNotation.MONE.value,
            index=self.source_frame.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION],
        )

    def _price_forming_data_initial_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.AMOUNT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY],
        )

    def _price_forming_data_remaining_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.AMOUNT].values,
            index=self.source_frame.index,
            columns=[OrderColumns.PRICE_FORMING_DATA_REMAINING_QUANTITY],
        )

    def _price_forming_data_traded_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.AMOUNT].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY,
                )
            ],
        )

    def _transaction_details_quantity(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.source_frame.loc[:, EzeSoftOmsAllocationsSourceColumns.AMOUNT].values,
            index=self.source_frame.index,
            columns=[
                add_prefix(
                    prefix=ModelPrefix.ORDER_STATE,
                    attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY,
                )
            ],
        )

    def _transaction_details_quantity_notation(self) -> pd.DataFrame:
        result: pd.DataFrame = run_map_conditional(
            source_frame=self.source_frame.loc[
                :, [EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY]
            ],
            params=MapConditionalParams(
                target_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                cases=[
                    Case(
                        query=f"`{EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY}`.str."
                        f"fullmatch('{EzeOMSEnum.CURRENCY.value}', case=False, na=False)",
                        value=QuantityNotation.MONE.value,
                    ),
                    Case(
                        query=f"~(`{EzeSoftOmsAllocationsSourceColumns.SECURITY_CURRENCY}`.str."
                        f"fullmatch('{EzeOMSEnum.CURRENCY.value}', case=False, na=False))",
                        value=QuantityNotation.UNIT.value,
                    ),
                ],
            ),
            skip_serializer=True,
        )
        return result

    def _transaction_details_ultimate_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data="XOFF",
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE],
        )

    def _transaction_details_venue(self) -> pd.DataFrame:
        return pd.DataFrame(
            data=self.pre_process_df.loc[:, EzeSoftOmsTempColumns.VENUE].values,
            index=self.pre_process_df.index,
            columns=[OrderColumns.TRANSACTION_DETAILS_VENUE],
        )

    def _market_identifiers_parties(self) -> pd.DataFrame:
        parties_source_frame = pd.concat(
            [
                self.source_frame.loc[
                    :,
                    [
                        EzeSoftOmsOrdersSourceColumns.TRADER_ID,
                        EzeSoftOmsAllocationsSourceColumns.BROKER,
                        EzeSoftOmsOrdersSourceColumns.MANAGER,
                        EzeSoftOmsExecutionsSourceColumns.TRADER,
                        EzeSoftOmsAllocationsSourceColumns.ACCOUNT,
                    ],
                ],
                self.target_df.loc[
                    :,
                    [
                        OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                    ],
                ],
                self.pre_process_df.loc[
                    :,
                    [
                        EzeSoftOmsTempColumns.EXECUTION_WITHIN_FIRM,
                    ],
                ],
            ],
            axis=1,
        )

        for column in parties_source_frame.columns.to_list():
            parties_source_frame[column] = parties_source_frame[column].apply(
                lambda x: f"{PartyPrefix.ID}{x}" if pd.notnull(x) else x
            )

        result: pd.DataFrame = run_generic_order_party_identifiers(
            source_frame=parties_source_frame,
            params=ParamsPartyIdentifiers(
                target_attribute=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                executing_entity_identifier=EzeSoftOmsOrdersSourceColumns.BROKER,
                trader_identifier="",
                client_identifier=EzeSoftOmsAllocationsSourceColumns.ACCOUNT,
                counterparty_identifier=EzeSoftOmsAllocationsSourceColumns.BROKER,
                investment_decision_within_firm_identifier=EzeSoftOmsOrdersSourceColumns.MANAGER,
                execution_within_firm_identifier=EzeSoftOmsTempColumns.EXECUTION_WITHIN_FIRM,
                buyer_identifier=EzeSoftOmsOrdersSourceColumns.BROKER,
                seller_identifier=EzeSoftOmsAllocationsSourceColumns.BROKER,
                create_fallback_fields=True,
                buy_sell_side_attribute=OrderColumns.EXECUTION_DETAILS_BUY_SELL_INDICATOR,
                use_buy_mask_for_buyer_seller=True,
            ),
            skip_serializer=True,
        )
        return result

    def _market_identifiers_instrument(self) -> pd.DataFrame:
        result: pd.DataFrame = run_instrument_identifiers(
            source_frame=pd.concat(
                [self.source_frame, self.pre_process_df, self.target_df], axis=1
            ),
            params=ParamsInstrumentIdentifiers(
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                notional_currency_2_attribute=OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                expiry_date_attribute=EzeSoftOmsTempColumns.EXPIRY_DATE_CONVERTED,
                isin_attribute=EzeSoftOmsTempColumns.ISIN,
                option_strike_price_attribute=EzeSoftOmsTempColumns.STRIKE_PRICE,
                option_type_attribute=EzeSoftOmsTempColumns.OPTION_TYPE,
                underlying_symbol_expiry_code_attribute=EzeSoftOmsTempColumns.UNDERLYING_SYMBOL_EXPIRY_CODE,
                underlying_symbol_attribute=EzeSoftOmsTempColumns.UNDERLYING_SYMBOL,
                asset_class_attribute=EzeSoftOmsTempColumns.ASSET_CLASS,
                retain_task_inputs=True,
            ),
            skip_serializer=True,
        )
        return result

    def _market_identifiers(self) -> pd.DataFrame:
        result: pd.DataFrame = run_merge_market_identifiers(
            source_frame=self.target_df,
            params=ParamsMergeMarketIdentifiers(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS,
                instrument_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                parties_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES,
            ),
            skip_serializer=True,
        )
        return result

    # --- Postprocessing --- #

    def _post_process(self):
        self.target_df.loc[:, TempColumns.FB_IS_CREATED_THROUGH_FALLBACK] = True
        self.target_df.loc[:, OrderTempColumns.NEWO_IN_FILE] = self.pre_process_df.loc[
            :, OrderTempColumns.NEWO_IN_FILE
        ]
        self.target_df.loc[:, TempColumns.ISIN] = self.pre_process_df.loc[
            :, EzeSoftOmsTempColumns.ISIN
        ]
        self.target_df.loc[:, TempColumns.SYMBOL] = self.source_frame.loc[
            :, EzeSoftOmsAllocationsSourceColumns.SYMBOL
        ]

    # --- NOT IMPLEMENTED --- #
    def _transaction_details_settlement_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_quantity_currency(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price(self) -> pd.DataFrame:
        pass

    def _transaction_details_price(self) -> pd.DataFrame:
        pass

    def _execution_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _transaction_details_trading_capacity(self) -> pd.DataFrame:
        pass

    def _is_repo(self):
        pass

    def _execution_details_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_routing_strategy(self) -> pd.DataFrame:
        pass

    def _execution_details_stop_price(self) -> pd.DataFrame:
        pass

    def _execution_details_aggregated_order(self) -> pd.DataFrame:
        pass

    def _order_identifiers_internal_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_average(self) -> pd.DataFrame:
        pass

    def _order_class(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_transaction_id_code(self) -> pd.DataFrame:
        pass

    def _multi_leg_reporting_type(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_received(self) -> pd.DataFrame:
        pass

    def _transaction_details_outgoing_order_addl_info(self) -> pd.DataFrame:
        pass

    def _timestamps_internal_order_submitted(self) -> pd.DataFrame:
        pass

    def _timestamps_external_order_received(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_date(self) -> pd.DataFrame:
        pass

    def _execution_details_order_type(self) -> pd.DataFrame:
        pass

    def _mar_details_is_personal_account_dealing(self) -> pd.DataFrame:
        pass

    def _execution_details_validity_period(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_aggressive_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_additional_limit_price(self) -> pd.DataFrame:
        pass

    def _execution_details_pegged_limit_price(self) -> pd.DataFrame:
        pass

    def _price_forming_data_display_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_modified_quantity(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_not_applicable(self) -> pd.DataFrame:
        pass

    def _price_forming_data_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_price_pending(self) -> pd.DataFrame:
        pass

    def _transaction_details_pricing_details_percent_vs_market(self) -> pd.DataFrame:
        pass

    def _execution_details_client_additional_info(self) -> pd.DataFrame:
        pass

    def _execution_details_liquidity_provision_activity(self) -> pd.DataFrame:
        pass

    def _execution_details_mes_first_execution_only(self) -> pd.DataFrame:
        pass

    def _execution_details_min_acceptable_quantity(self) -> pd.DataFrame:
        pass

    def _execution_details_min_executable_size(self) -> pd.DataFrame:
        pass

    def _execution_details_order_restriction(self) -> pd.DataFrame:
        pass

    def _execution_details_passive_only_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_self_execution_prevention(self) -> pd.DataFrame:
        pass

    def _execution_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _execution_details_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _execution_details_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _financing_type(self) -> pd.DataFrame:
        pass

    def _is_discretionary(self) -> pd.DataFrame:
        pass

    def _is_iceberg(self) -> pd.DataFrame:
        pass

    def _jurisdiction_business_line(self) -> pd.DataFrame:
        pass

    def _jurisdiction_country(self) -> pd.DataFrame:
        pass

    def _jurisdiction_legal_entity(self) -> pd.DataFrame:
        pass

    def _order_identifiers_initial_order_designation(self) -> pd.DataFrame:
        pass

    def _order_identifiers_order_routing_code(self) -> pd.DataFrame:
        pass

    def _order_identifiers_sequence_number(self) -> pd.DataFrame:
        pass

    def _order_identifiers_trading_venue_order_id_code(self) -> pd.DataFrame:
        pass

    def _timestamps_validity_period(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_commodity_derivative_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_responsible_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_decision_maker_supervising_branch_country(
        self,
    ) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_otc_post_trade_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_securities_financing_txn_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_short_selling_indicator(self) -> pd.DataFrame:
        pass

    def _traders_algos_waivers_indicators_waiver_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_basket_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_branch_membership_country(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_currency(self) -> pd.DataFrame:
        pass

    def _transaction_details_commission_amount_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_complex_trade_component_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_cross_indicator(self) -> pd.DataFrame:
        pass

    def _transaction_details_cumulative_quantity(self) -> pd.DataFrame:
        pass

    def _transaction_details_derivative_notional_change(self) -> pd.DataFrame:
        pass

    def _transaction_details_net_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_order_id_code(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_effect(self) -> pd.DataFrame:
        pass

    def _transaction_details_position_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_amount(self) -> pd.DataFrame:
        pass

    def _transaction_details_settlement_type(self) -> pd.DataFrame:
        pass

    def _transaction_details_swap_directionalities(self) -> pd.DataFrame:
        pass

    def _transaction_details_trail_id(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment(self) -> pd.DataFrame:
        pass

    def _transaction_details_upfront_payment_currency(self) -> pd.DataFrame:
        pass

    def _process_cache(self) -> None:
        pass
