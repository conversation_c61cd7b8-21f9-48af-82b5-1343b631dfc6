import gnupg  # type: ignore
import io
import logging

logger = logging.getLogger("gnupg")
logger.setLevel(logging.INFO)


class GPGHandler:
    def __init__(
        self,
        gpg_private_ascii: str,
        passphrase: str | None = None,
        ignore_mdc_error: bool = False,
    ):
        self.gpg_key = (
            gnupg.GPG(options=["--ignore-mdc-error"]) if ignore_mdc_error else gnupg.GPG()
        )
        self.passphrase = passphrase

        self.gpg_key.import_keys(gpg_private_ascii)

        try:
            self.gpg_key_id = self.gpg_key.list_keys()[-1]["keyid"]
            logger.info(f"Using GPG_KEY_ID={self.gpg_key_id}")
        except IndexError:
            raise ValueError("No GPG key found")

    def decrypt_file(self, encrypted_file_path: str):
        """decrypts on the basis of encrypted file path.

        Params:
            encrypted_file_path: (str) encrypted file path
        """
        result = self.gpg_key.decrypt_file(
            encrypted_file_path, always_trust=True, passphrase=self.passphrase
        )
        if not result.ok:
            raise ValueError(f"Decryption failed. KEY_ID={result.key_id}, Status - {result.status}")
        decrypted_file_object = io.BytesIO(result.data)
        decrypted_file_object.seek(0)
        return decrypted_file_object

    def decrypt_file_object(self, encrypted_file_object: io.BytesIO):
        """
        decrypts on the basis of encrypted file object
        Params:
            encrypted_file_object: (bytes) encrypted file object
        """
        encrypted_file_object.seek(0)
        result = self.gpg_key.decrypt_file(
            encrypted_file_object, always_trust=True, passphrase=self.passphrase
        )
        if not result.ok:
            raise ValueError(f"Decryption failed. KEY_ID={result.key_id}, Status - {result.status}")
        decrypted_file_object = io.BytesIO(result.data)
        decrypted_file_object.seek(0)
        return decrypted_file_object

    def encrypt_file_object(self, file_object: io.BytesIO) -> io.BytesIO:
        """Encrypt a file object."""
        encrypted_file_object = io.BytesIO()
        encrypted_file_object.write(
            self.gpg_key.encrypt(file_object.getvalue(), self.gpg_key_id, always_trust=True).data
        )
        encrypted_file_object.seek(0)
        return encrypted_file_object
