# type: ignore
import dataclasses
import fsspec
import json
import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.app_metrics_enum import VoiceTransformAppMetricsEnum
from aries_se_comms_tasks.transcription.app_metrics_enum import IVTranscriptionMetricsEnum
from aries_se_comms_tasks.transcription.intelligent_voice.get_iv_db_config import (
    IntelligentVoiceConfigFields,
    get_iv_config_for_tenant,
    validate_config_enums,
)
from aries_se_comms_tasks.transcription.intelligent_voice.iv_api_wrapper import (
    IntelligentVoiceAPIWrapper,
)
from aries_se_comms_tasks.transcription.intelligent_voice.static import (
    IntelligentVoiceTranscriptFields,
)
from aries_se_comms_tasks.transcription.static import TranscriptionFields
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config
from aries_task_link.models import AriesTaskInput
from integration_audit.auditor import Audi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s, upsert_audit
from se_data_lake.lake_path import get_ingest_path_for_in_progress_intelligent_voice_jobs
from se_elastic_schema.models import Call
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from typing import List, Optional, Set

logger = logging.getLogger(__name__)


class SkipIfIncompleteRecords(TaskException):
    pass


class FailIfMissingColumns(TaskException):
    pass


class TempColumns:
    FILE_PATH = "file_path"


class IvSubmitTranscriptionJobs(IntegrationTask):
    """This task submits transcription jobs to Intelligent Voice for each call
    in the input data frame.

    Once it submits the jobs, it writes marker files in cloud (file name = IV item id)
    to indicate which jobs are currently in progress, and which &ids these jobs
    belong to
    """

    def __init__(
        self, iv_username: str, iv_password: str, intelligent_voice_endpoint: str, *args, **kwargs
    ):
        super().__init__(*args, **kwargs)
        self.iv_api = IntelligentVoiceAPIWrapper(
            intelligent_voice_endpoint=intelligent_voice_endpoint,
            iv_username=iv_username,
            iv_password=iv_password,
        )

    def _run(
        self,
        source_frame: pd.DataFrame,
        realm: str,
        tenant: str,
        cloud_provider_prefix: str,
        aries_task_input: AriesTaskInput,
        streamed: bool,
    ) -> pd.DataFrame:
        """
        :param source_frame: Pandas DataFrame with the &id of Intelligent Voice and the
        attachment bucket and key, duration
        :param realm: tenant realm i.e "pinafore.dev.steeleye.co"
        :param tenant: Tenant
        :param aries_task_input: AriesTaskInput
        """
        # EsConfig
        es_client: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )

        mandatory_columns = {
            TranscriptionFields.ATTACHMENT_KEY,
            TranscriptionFields.ATTACHMENT_BUCKET,
            es_client.meta.id,
            es_client.meta.hash,
            TranscriptionFields.DURATION,
        }
        target = self.preprocess_data(
            source_frame=source_frame,
            mandatory_columns=mandatory_columns,
        )

        target = self.drop_duplicate_calls(id_field=es_client.meta.id, df=target)

        target[TempColumns.FILE_PATH] = (
            cloud_provider_prefix
            + target[TranscriptionFields.ATTACHMENT_BUCKET]
            + "/"
            + target[TranscriptionFields.ATTACHMENT_KEY]
        )

        # Get the intelligent voice config from config DB for the current tenant
        iv_config = get_iv_config_for_tenant(tenant=tenant)

        # Assign the values obtained to a data class, taking care to discard extra
        # keys from iv_config. This step also validates the config.

        field_names = set(f.name for f in dataclasses.fields(IntelligentVoiceConfigFields))
        # Validate enum values in the config
        validate_config_enums(iv_config=iv_config)
        iv_config_fields = IntelligentVoiceConfigFields(
            **{k: v for k, v in iv_config.items() if k in field_names}
        )

        # Start submitting jobs for each call
        # TODO: Later, change this to use asyncio/multiprocessing to submit the jobs
        target.loc[:, IntelligentVoiceTranscriptFields.ITEM_ID] = target.loc[
            :, [TranscriptionFields.ATTACHMENT_KEY, es_client.meta.id, TempColumns.FILE_PATH]
        ].apply(
            lambda x: self.iv_api.create_item(
                file_path=x[TempColumns.FILE_PATH],
                meta_id=x[es_client.meta.id],
                iv_config_fields=iv_config_fields,
            ),
            axis=1,
        )

        # If the item ID is null, we don't write the row to the output CSV, as there is no point.
        # Nothing can be looked up in the IV API flow if there is no item ID, so these will just
        # not be transcribed

        null_item_ids_mask = target[IntelligentVoiceTranscriptFields.ITEM_ID].isnull()
        call_ids_which_wont_be_transcribed = target.loc[
            null_item_ids_mask, es_client.meta.id
        ].to_list()
        target = target.loc[~null_item_ids_mask]

        if len(call_ids_which_wont_be_transcribed) > 0:
            self.update_app_metrics(
                field=VoiceTransformAppMetricsEnum.CALLS_WITHOUT_TRANSCRIPTS_COUNT,
                value=len(call_ids_which_wont_be_transcribed),
            )
            upsert_audit(
                audit_path=self.audit_path,
                streamed=streamed,
                input_data={
                    key_: {
                        AuditorStaticFields.STATUS: [
                            "Calls will not be transcribed because the Intelligent Voice Server"
                            "returned an error while submitting the transcription job"
                        ]
                    }
                    for key_ in call_ids_which_wont_be_transcribed
                },
                models=[Call],
            )
        marker_file_columns_list = target[
            [
                es_client.meta.id,
                es_client.meta.hash,
                IntelligentVoiceTranscriptFields.ITEM_ID,
                TranscriptionFields.DURATION,
                TranscriptionFields.ATTACHMENT_KEY,
            ]
        ].to_dict(orient="records")

        # Log all the item IDs
        item_ids = target.loc[:, IntelligentVoiceTranscriptFields.ITEM_ID].tolist()
        logger.info(f"Number of items submitted to IV for transcription: {len(item_ids)}")
        logger.info(f"Item IDs submitted to IV for transcription: {item_ids}")

        self.update_app_metrics(
            field=IVTranscriptionMetricsEnum.JOBS_SUBMITTED_COUNT, value=target.shape[0]
        )

        ingest_path = get_ingest_path_for_in_progress_intelligent_voice_jobs(
            workflow_trace_id=aries_task_input.workflow.trace_id
        )
        self._write_marker_files(
            marker_file_columns_list=marker_file_columns_list,
            es_client=es_client,
            realm=realm,
            ingest_path=ingest_path,
            cloud_provider_prefix=cloud_provider_prefix,
        )
        return target

    @staticmethod
    def _write_marker_files(
        marker_file_columns_list: List[dict],
        es_client: ElasticsearchRepository,
        ingest_path: str,
        realm: str,
        cloud_provider_prefix: str,
    ):
        """This function writes marker files to cloud for all the calls which
        have been submitted for transcription. The name of each marker file
        contains the item ID, and the file itself contains the & id of the
        call.

        :param marker_file_columns_list: list of dictionaries where each dict contains
        columns to be written in the marker file
        :param es_client: Elasticsearch client, used to get the &id column
        :param ingest_path: Ingest path to which the files are written
        :param realm: Realm, used to get the bucket
        """

        # TODO change this to upload to cloud with asyncio
        for dict_ in marker_file_columns_list:
            amp_id = dict_.get(es_client.meta.id)
            amp_hash = dict_.get(es_client.meta.hash)
            item_id = dict_.get(IntelligentVoiceTranscriptFields.ITEM_ID)
            duration = dict_.get(TranscriptionFields.DURATION)
            recording_key = dict_.get(TranscriptionFields.ATTACHMENT_KEY)
            filepath = f"{cloud_provider_prefix}{realm}/{ingest_path}{item_id}.json"

            json_content = {
                es_client.meta.id: amp_id,
                TranscriptionFields.DURATION: duration,
                es_client.meta.hash: amp_hash,
                TranscriptionFields.ATTACHMENT_KEY: recording_key,
            }
            with fsspec.open(filepath, "w") as file:
                file.write(json.dumps(json_content))
            logger.info(f"Wrote file {filepath} for call with &id={amp_id} and item_id={item_id}")

    @staticmethod
    def drop_duplicate_calls(id_field: str, df: pd.DataFrame) -> pd.DataFrame:
        """Drop duplicate calls by &id.

        :param id_field: Name of ID field i.e. &id
        :param df: Pandas DataFrame with Call data
        :return: Pandas DataFrame with Call data without duplicate
        """
        initial_size = df.shape[0]
        df = df.drop_duplicates(subset=[id_field])
        final_size = df.shape[0]
        if final_size != initial_size:
            logger.info(
                f"{initial_size - final_size} duplicate calls were dropped and"
                f" will not be transcribed"
            )
        return df

    @staticmethod
    def preprocess_data(source_frame: pd.DataFrame, mandatory_columns: Set) -> pd.DataFrame:
        """Filter only the set of columns necessary for IV transcription, and
        audit failure scenarios.

        :param source_frame: Pandas DataFrame with necessary data for IV
         transcription
        :param mandatory_columns: Set of mandatory columns for IV transcription
        :return: Pandas DataFrame with only the necessary data for IV
         transcription (valid records only)
        """

        columns_mask = source_frame.columns.isin(mandatory_columns)
        target = source_frame.loc[
            :, columns_mask
        ].dropna()  # all columns must be populated for a given row

        if target.empty:
            message = (
                f"All {source_frame.shape[0]} records are missing at least one of"
                f" the mandatory columns: "
                f"{mandatory_columns}. Transcription will be skipped"
            )
            raise SkipIfIncompleteRecords(message)

        if target.shape[1] != len(mandatory_columns):
            message = (
                f"The following columns must be populated to trigger IV Transcription: "
                f"{mandatory_columns - set(target.columns.tolist())}"
            )
            raise FailIfMissingColumns(message)

        target_size = target.shape[0]
        source_size = source_frame.shape[0]
        if target_size != source_size:
            difference = source_size - target_size
            message = (
                f"{difference} records will not be transcribed due to missing at least one of the "
                f"mandatory columns: {mandatory_columns}"
            )
            logger.info(message)

        return target


def run_iv_submit_transcription_jobs(
    source_frame: pd.DataFrame,
    realm: str,
    tenant: str,
    cloud_provider_prefix: str,
    iv_username: str,
    iv_password: str,
    intelligent_voice_endpoint: str,
    aries_task_input: AriesTaskInput,
    streamed: bool,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> pd.DataFrame:
    task = IvSubmitTranscriptionJobs(
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        iv_username=iv_username,
        iv_password=iv_password,
        intelligent_voice_endpoint=intelligent_voice_endpoint,
    )
    return task.run(
        source_frame=source_frame,
        realm=realm,
        tenant=tenant,
        cloud_provider_prefix=cloud_provider_prefix,
        aries_task_input=aries_task_input,
        streamed=streamed,
    )
