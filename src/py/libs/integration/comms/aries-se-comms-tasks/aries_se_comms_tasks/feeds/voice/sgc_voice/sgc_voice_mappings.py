# mypy: disable-error-code="attr-defined"
import logging
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.sgc_voice.static import (
    FLOW_NAME,
    METADATA_SOURCE_CLIENT,
    SGCApiResponseAttribute,
    SGCDatetimeFormat,
    SGCFileUrlColumns,
    SGCSourceColumns,
    SGCTempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    Params as ParticipantIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import Params as ConvertDatetimeParams
from aries_se_core_tasks.datetime.convert_datetime import run_convert_datetime
from aries_se_core_tasks.datetime.increment_datetime import Params as ParamsIncrementDatetime
from aries_se_core_tasks.datetime.increment_datetime import run_increment_datetime
from aries_se_core_tasks.transform.map.map_attribute import Params as MapAttributeParams
from aries_se_core_tasks.transform.map.map_attribute import run_map_attribute
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_data_lake.cloud_utils import get_bucket, get_key

logger = logging.getLogger(__name__)


class SGCVoiceMappings(AbstractVoiceTransformations):
    """Transformations for SGC Advance calls."""

    def __init__(
        self,
        source_file_url: str,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.source_file_url: str = source_file_url
        self.pre_process_df: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)

    def _pre_process(self) -> None:
        self.source_frame: pd.DataFrame = add_missing_columns(
            dataframe=self.source_frame, columns=SGCSourceColumns().all()
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self._call_participants_identifiers(),
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.call_duration()
        self.connected()

        # Identifiers
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_device_id()
        self.identifiers_to_device_id()
        self.identifiers_from_id()
        self.identifiers_to_ids()

        # Timestamps
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_start()
        self.timestamps_timestamp_connected()
        self.timestamps_local_timestamp_end()
        self.timestamps_timestamp_end()

        self.id()

        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.source_index()
        self.source_key()
        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()

        return self.target_df

    def _call_duration(self) -> pd.Series:
        """Used to populate CallColumns.CALL_DURATION from the input recording
        files."""
        return self.source_frame.loc[:, SGCApiResponseAttribute.DURATION]

    def _connected(self) -> pd.Series:
        """Populates CallColumns.CONNECTED."""
        return pd.Series(
            data=True,
            index=self.source_frame.index,
            name=CallColumns.CONNECTED,
        )

    def _id(self) -> pd.Series:
        """Populates CallColumns.ID."""
        return self.source_frame.loc[:, SGCApiResponseAttribute.SID]

    def _identifiers_all_country_codes(self) -> pd.Series:
        """Used to populate CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES."""
        return self.pre_process_df[CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        """Populates all_ids from the from and to ids."""
        return self.pre_process_df[CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_device_id(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_FROM_DEVICE_ID"""
        return self.pre_process_df[CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_to_device_id(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_TO_DEVICE_ID"""
        return self.pre_process_df[CallColumns.IDENTIFIERS_TO_IDS].str.join(";")

    def _identifiers_from_id(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_FROM_ID."""
        return self.pre_process_df[CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_to_ids(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df[CallColumns.IDENTIFIERS_TO_IDS]

    def _metadata_source_client(self) -> pd.Series:
        """Populates CallColumns.METADATA_SOURCE_CLIENT"""
        return pd.Series(
            data=METADATA_SOURCE_CLIENT,
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_CLIENT,
        )

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return self.source_frame[SGCFileUrlColumns.METADATA_FILE_URL].apply(lambda x: get_bucket(x))

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return self.source_frame[SGCFileUrlColumns.METADATA_FILE_URL].apply(lambda x: get_key(x))

    def _source_index(self) -> pd.Series:
        """Populates source index."""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        """Populates Source Key."""
        return pd.Series(
            data=self.source_file_url,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END.

        MUST RUN AFTER _call_duration AND
        _timestamps_local_timestamp_start
        """

        return run_increment_datetime(
            source_frame=self.target_df[
                [CallColumns.CALL_DURATION, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]
            ],
            params=ParamsIncrementDatetime(
                source_datetime_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                source_datetime_format=SGCDatetimeFormat.DATETIME,
                source_incrementor_attribute=CallColumns.CALL_DURATION,
                source_incrementor_unit="s",
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                source_attribute=SGCApiResponseAttribute.RECORDING_START,
                convert_to=ConvertTo.DATETIME.value,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED."""
        return self.target_df[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_timestamp_end(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_END."""
        return self.target_df[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Populates CallColumns.TIMESTAMPS_TIMESTAMP_START.

        MUST RUN AFTER _timestamps_local_timestamp_start.
        """
        return self.target_df[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET."""
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + FLOW_NAME
            + "/"
            + self.target_df[CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _call_participants_identifiers(self) -> pd.DataFrame:
        """Calls ParticipantsIdentifiers and returns the results in a
        dataframe."""

        to_ids_df = run_map_attribute(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SGCApiResponseAttribute.CALLED_CLI,
                target_attribute=SGCTempColumns.TO_IDS_LIST,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=";",
            ),
            skip_serializer=True,
        )

        return run_participant_identifiers(  # type: ignore[no-any-return]
            source_frame=pd.concat([to_ids_df, self.source_frame], axis=1),
            params=ParticipantIdentifiersParams(
                source_from_identifier=SGCApiResponseAttribute.CALLING_CLI,
                source_to_identifiers=SGCTempColumns.TO_IDS_LIST,
            ),
            skip_serializer=True,
        )

    def _has_attachment(self) -> pd.Series:
        """NOT NEEDED."""

    def _post_process(self):
        """NOT NEEDED."""

    def _attachments(self):
        """NOT NEEDED."""

    def _call_duration_speaking(self):
        """NOT NEEDED."""

    def _call_type(self):
        """NOT NEEDED."""

    def _charge(self):
        """NOT NEEDED."""

    def _direction(self):
        """NOT NEEDED."""

    def _fault(self):
        """NOT NEEDED."""

    def _identifiers_all_domains(self):
        """NOT NEEDED."""

    def _identifiers_bcc_ids(self):
        """NOT NEEDED."""

    def _identifiers_cc_ids(self):
        """NOT NEEDED."""

    def _identifiers_from_id_addl_info(self):
        """NOT NEEDED."""

    def _identifiers_from_ip(self):
        """NOT NEEDED."""

    def _identifiers_from_user_id(self):
        """NOT NEEDED."""

    def _identifiers_host_id(self):
        """NOT NEEDED."""

    def _identifiers_on_behalf_of(self):
        """NOT NEEDED."""

    def _identifiers_to_ids_addl_info(self):
        """NOT NEEDED."""

    def _identifiers_to_ip(self):
        """NOT NEEDED."""

    def _identifiers_to_user_id(self):
        """NOT NEEDED."""

    def _is_multi_channel(self):
        """NOT NEEDED."""

    def _is_dealer_board(self):
        """NOT NEEDED."""

    def _join_reason(self):
        """NOT NEEDED."""

    def _meta_model(self):
        """NOT NEEDED."""

    def _metadata_content_type(self):
        """NOT NEEDED."""

    def _metadata_encoding_type(self):
        """NOT NEEDED."""

    def _metadata_header(self):
        """NOT NEEDED."""

    def _metadata_in_reply_to(self):
        """NOT NEEDED."""

    def _metadata_message_id(self):
        """NOT NEEDED."""

    def _metadata_reference_id(self):
        """NOT NEEDED."""

    def _metadata_size_in_bytes(self):
        """NOT NEEDED."""

    def _metadata_source_device_type(self):
        """NOT NEEDED."""

    def _participants(self):
        """NOT NEEDED."""

    def _rate(self):
        """NOT NEEDED."""

    def _transcribed(self):
        """NOT NEEDED."""

    def _voice_file(self):
        """NOT NEEDED."""

    def _timestamps_created(self):
        """NOT NEEDED."""

    def _timestamps_duration_unit(self):
        """NOT NEEDED."""

    def _timestamps_duration_value(self):
        """NOT NEEDED."""

    def _internal(self):
        """NOT NEEDED."""

    def _conference_call(self):
        """NOT NEEDED."""

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
