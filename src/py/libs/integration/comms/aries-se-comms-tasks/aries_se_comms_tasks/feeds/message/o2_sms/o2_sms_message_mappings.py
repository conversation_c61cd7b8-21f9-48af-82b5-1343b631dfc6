import logging
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_message_transformations import (
    AbstractMessageTransformations,
)
from aries_se_comms_tasks.feeds.message.o2_sms.static import O2SmsSourceColumns, O2SmsTempColumns
from aries_se_comms_tasks.feeds.message.o2_sms.utils import SkipIfEmptyO2MappingsSourceFrame
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined]
    Params as ParticipantIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined]
    run_participant_identifiers,
)
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]
    run_convert_datetime,
)
from aries_se_core_tasks.io.utils import FileInfo
from datetime import datetime
from pathlib import Path
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ConvertDatetimeParams
from se_core_tasks.utils.datetime import DatetimeFormat

logger_ = logging.getLogger(__name__)


class O2SMSMessageMappings(AbstractMessageTransformations):
    def __init__(
        self,
        source_frame: pd.DataFrame,
        realm: str,
        file_uri: str,
        file_metadata: FileInfo,
        **kwargs,
    ):
        if source_frame.empty:
            raise SkipIfEmptyO2MappingsSourceFrame("No Messages found. Nothing to transform.")
        super().__init__(source_frame=source_frame, logger=logger_, realm=realm)
        self.file_uri: str = file_uri
        self.file_metadata: FileInfo = file_metadata

    def _pre_process(self) -> None:
        self.pre_process_df: pd.DataFrame = pd.concat(
            objs=[
                self.pre_process_df,
                self._temp_from_and_to_sorted_string(),
                self._temp_to_as_list(),
                self._temp_timestamp(),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            objs=[self.pre_process_df, self._temp_participants()], axis=1
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.body_display_text()
        self.body_text()
        self.chat_type()
        self.source_key()
        self.room_id()
        self.room_name()

        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()

        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.metadata_message_id()
        self.metadata_thread_id()
        self.metadata_size_in_bytes()
        self.metadata_source_client()
        self.metadata_source_file_info_content_length()
        self.metadata_source_file_info_content_md5()
        self.metadata_source_file_info_version_id()
        self.metadata_source_file_info_last_modified()
        self.metadata_source_file_info_processed()

        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_start()

        return self.target_df

    def _body_text(self) -> pd.Series:
        return self.source_frame.loc[:, O2SmsSourceColumns.MESSAGE]  # type: ignore[no-any-return]

    def _body_display_text(self) -> pd.Series:
        return self.source_frame.loc[:, O2SmsSourceColumns.MESSAGE]  # type: ignore[no-any-return]

    def _chat_type(self) -> pd.Series:
        return pd.Series(data=["SMS"] * len(self.source_frame), index=self.source_frame.index)

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=[self.file_uri] * len(self.source_frame), index=self.source_frame.index
        )

    def _room_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.FROM_AND_TO_SORTED]

    def _room_name(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.FROM_AND_TO_SORTED]

    def _identifiers_all_country_codes(self):
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self):
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_to_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self):
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=[Path(self.file_uri).parts[1]] * len(self.source_frame),
            index=self.source_frame.index,
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return pd.Series(
            data=["/".join(Path(self.file_uri).parts[2:])] * len(self.source_frame),
            index=self.source_frame.index,
        )

    def _metadata_message_id(self) -> pd.Series:
        return self.source_frame.loc[:, O2SmsSourceColumns.MESSAGE_ID]  # type: ignore[no-any-return]

    def _metadata_thread_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.FROM_AND_TO_SORTED]

    def _metadata_size_in_bytes(self) -> pd.Series:
        return pd.Series(
            data=[self.file_metadata.size] * len(self.source_frame), index=self.source_frame.index
        )

    def _metadata_source_client(self) -> pd.Series:
        return pd.Series(
            data=["O2Mobile_SMS"] * len(self.source_frame), index=self.source_frame.index
        )

    def _metadata_source_file_info_content_length(self) -> pd.Series:
        return pd.Series(
            data=[self.file_metadata.size] * len(self.source_frame), index=self.source_frame.index
        )

    def _metadata_source_file_info_content_md5(self) -> pd.Series:
        e_tag = self.file_metadata.e_tag

        return pd.Series(data=[e_tag] * len(self.source_frame), index=self.source_frame.index)

    def _metadata_source_file_info_version_id(self) -> pd.Series:
        version_id = self.file_metadata.version_id

        return pd.Series(data=[version_id] * len(self.source_frame), index=self.source_frame.index)

    def _metadata_source_file_info_last_modified(self) -> pd.Series:
        return pd.Series(
            data=[str(self.file_metadata.last_modified)] * len(self.source_frame),
            index=self.source_frame.index,
        )

    def _metadata_source_file_info_processed(self) -> pd.Series:
        return pd.Series(
            data=[datetime.utcnow().strftime(DatetimeFormat.DATETIME)] * len(self.source_frame),
            index=self.source_frame.index,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.TIMESTAMP]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.TIMESTAMP]

    def _timestamps_timestamp_end(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.TIMESTAMP]

    def _timestamps_timestamp_start(self) -> pd.Series:
        return self.pre_process_df.loc[:, O2SmsTempColumns.TIMESTAMP]

    # Helper methods
    def _temp_from_and_to_sorted_string(self) -> pd.Series:
        """Converts From and To values into a single string to use as thread
        id."""

        list_of_values = self.source_frame.loc[
            :, [O2SmsSourceColumns.FROM, O2SmsSourceColumns.TO]
        ].values.tolist()

        # list_of_values_as_string = [f"{sorted(x)[0]} and {sorted(x)[1]}" for x in list_of_values]
        list_of_values_as_string = []

        for x in list_of_values:
            if all([pd.isna(val) for val in x]):
                list_of_values_as_string.append(pd.NA)
            else:
                list_of_values_as_string.append(f"{sorted(x)[0]} and {sorted(x)[1]}")  # type: ignore[arg-type]

        return pd.Series(
            data=list_of_values_as_string,
            index=self.source_frame.index,
            name=O2SmsTempColumns.FROM_AND_TO_SORTED,
        )

    def _temp_to_as_list(self) -> pd.Series:
        """TO column needs to be a list in participants identifiers, converting
        here."""
        return pd.Series(
            data=[
                [x] if not pd.isna(x) else pd.NA
                for x in self.source_frame.loc[:, O2SmsSourceColumns.TO]
            ],
            index=self.source_frame.index,
            name=O2SmsTempColumns.TO_AS_LIST,
        )

    def _temp_timestamp(self) -> pd.DataFrame:
        """Convert timestamp to default format."""
        date_time: pd.DataFrame = run_convert_datetime(
            source_frame=self.source_frame.loc[:, [O2SmsSourceColumns.DATE_TIME]],
            params=ConvertDatetimeParams(
                source_attribute=O2SmsSourceColumns.DATE_TIME,
                source_attribute_format="%Y-%m-%d %H:%M:%S",
                convert_to=ConvertTo.DATETIME,
                target_attribute=O2SmsTempColumns.TIMESTAMP,
            ),
            skip_serializer=True,
        )
        return date_time

    def _temp_participants(self) -> pd.DataFrame:
        """Populates for:

        identifiers.fromId identifiers.toIds identifiers.fromIdAddlIndo
        identifiers.toIdsAddlInfo identifiers.allIds
        identifiers.allCountryCodes
        """
        participants: pd.DataFrame = run_participant_identifiers(
            source_frame=pd.concat(
                objs=[
                    self.source_frame.loc[:, O2SmsSourceColumns.FROM],
                    self.pre_process_df.loc[:, O2SmsTempColumns.TO_AS_LIST],
                ],
                axis=1,
            ),
            params=ParticipantIdentifiersParams(
                source_from_identifier=O2SmsSourceColumns.FROM,
                source_to_identifiers=O2SmsTempColumns.TO_AS_LIST,
            ),
            skip_serializer=True,
        )

        return participants

    # Unused Methods
    def _post_process(self) -> None:
        raise NotImplementedError

    def _attachments(self) -> pd.Series:
        raise NotImplementedError

    def _body_edits(self) -> pd.Series:
        raise NotImplementedError

    def _body_type(self) -> pd.Series:
        raise NotImplementedError

    def _counts_reply_count(self) -> pd.Series:
        raise NotImplementedError

    def _counts_reply_user_count(self) -> pd.Series:
        raise NotImplementedError

    def _has_attachment(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_all_domains(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_domains(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_from_device_id(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_from_user_id(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_mentioned_ids(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_on_behalf_of(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_saved_by_ids(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_to_device_id(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_to_ip(self) -> pd.Series:
        raise NotImplementedError

    def _identifiers_to_user_id(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_content_type(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_encoding_type(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_header(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_is_edited(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_is_deleted(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_is_pinned(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_in_reply_to(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_original_message_id(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_reacted_to(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_sub_thread_id(self) -> pd.Series:
        raise NotImplementedError

    def _meta_model(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_reference_id(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_source_device_type(self) -> pd.Series:
        raise NotImplementedError

    def _metadata_source_file_info_metadata(self) -> pd.Series:
        raise NotImplementedError

    def _participants(self) -> pd.Series:
        raise NotImplementedError

    def _timestamps_created(self) -> pd.Series:
        raise NotImplementedError

    def _timestamps_timestamp_connected(self) -> pd.Series:
        raise NotImplementedError
