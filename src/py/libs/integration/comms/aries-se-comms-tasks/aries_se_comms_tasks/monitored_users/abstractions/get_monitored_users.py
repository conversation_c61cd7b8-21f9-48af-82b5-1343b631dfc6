import logging
from aries_se_comms_tasks.monitored_users.abstractions import monitored_user_maps
from aries_se_comms_tasks.monitored_users.abstractions.abstract_monitored_users import (
    AbstractMonitoredUsers,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_enums.cloud import Cloud<PERSON>roviderEnum
from typing import Dict, Optional, Type

logger = logging.getLogger(__name__)


class FailIfNoMonitoredUsersMapForWorkflow(TaskException):
    pass


class GetMonitoredUsers(IntegrationTask):
    """This class gets the monitored users based on the workflow and tenant."""

    def _run(
        self,
        workflow: str,
        tenant: str,
        stack: str,
        cloud_provider: CloudProviderEnum,
        streamed: bool,
        audit_monitored_users: bool,
        **kwargs,
    ) -> Type[AbstractMonitoredUsers]:
        monitored_users_map: Dict[str, monitored_user_maps.MonitoredUserMap] = {
            "bloomberg": monitored_user_maps.bloomberg_monitored_users_map,
            "ice_chat": monitored_user_maps.ice_chat_monitored_users_map,
            "refinitiv_tr_eikon_chat": monitored_user_maps.refinitiv_tr_eikon_monitored_users_map,
            "symphony_xml_chat": monitored_user_maps.symphony_xml_monitored_users_map,
        }

        monitored_users_task = monitored_users_map.get(workflow)
        if monitored_users_task is None:
            raise FailIfNoMonitoredUsersMapForWorkflow(f"No monitored users class for {workflow}.")
        mu_instance = monitored_users_task.get_monitored_users_class(tenant=tenant)(
            logger=logger,
            tenant=tenant,
            stack=stack,
            streamed=streamed,
            cloud_provider=cloud_provider,
            audit_path=self.audit_path,
            **kwargs,
        )
        mu_instance.set_monitored_user_filtering_flag()
        mu_instance.fetch_monitored_users(tenant=tenant, **kwargs)

        if audit_monitored_users:
            mu_instance.audit_monitored_users()

        return mu_instance  # type: ignore[no-any-return]


def run_get_monitored_users(
    workflow: str,
    tenant: str,
    stack: str,
    cloud_provider: CloudProviderEnum,
    streamed: bool,
    audit_monitored_users: bool,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = GetMonitoredUsers(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(
        workflow=workflow,
        tenant=tenant,
        stack=stack,
        cloud_provider=cloud_provider,
        streamed=streamed,
        audit_monitored_users=audit_monitored_users,
        **kwargs,
    )
