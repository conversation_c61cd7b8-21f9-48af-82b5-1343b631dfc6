from enum import Enum
from typing import List


class BloombergIb19EventTags:
    ATTACHMENT = "Attachment"
    MESSAGE = "Message"
    INVITE = "Invite"
    PARTICIPANT_ENTERED = "ParticipantEntered"
    PARTICIPANT_LEFT = "ParticipantLeft"


class BloombergTags:
    # IB19 + MSG
    CLIENT_ID_1 = "ClientID1"
    CORPORATE_EMAIL_ADDRESS = "CorporateEmailAddress"

    # IB19
    CONTENT = "Content"
    EMAIL_ADDRESS = "EmailAddress"
    INVITEE = "Invitee"
    INVITER = "Inviter"
    LOGIN_NAME = "LoginName"
    USER = "User"

    # MSG
    BLOOMBERG_EMAIL_ADDRESS = "BloombergEmailAddress"
    FORWARDED_TO = "ForwardedTo"
    RECIPIENT = "Recipient"
    SENDER = "Sender"
    USER_INFO = "UserInfo"

    @classmethod
    def get_ib19_user_id_tags(cls) -> List[str]:
        """Returns a list of sub-tags which form the involved user ids of an
        IB19 conversation. Note that these sub-tags are under:

        <Message><User> <Attachment><User> <ParticipantEntered><User>
        <ParticipantLeft><User> <Invite><Inviter> <Invite><Invitee>
        """
        return [
            cls.CLIENT_ID_1,
            cls.CORPORATE_EMAIL_ADDRESS,
            cls.EMAIL_ADDRESS,
            cls.LOGIN_NAME,
        ]

    @classmethod
    def get_msg_user_id_tags(cls) -> List[str]:
        """Returns a list of sub-tags which form the involved user ids of a MSG
        email. Note that these sub-tags are under:

        <Message><User> <Attachment><User> <ParticipantEntered><User>
        <ParticipantLeft><User> <Invite><Inviter> <Invite><Invitee>
        """
        return [
            cls.BLOOMBERG_EMAIL_ADDRESS,
            cls.CORPORATE_EMAIL_ADDRESS,
            cls.CLIENT_ID_1,
        ]


class BloombergFileType(Enum):
    IB19 = "ib19"
    MSG = "msg"
