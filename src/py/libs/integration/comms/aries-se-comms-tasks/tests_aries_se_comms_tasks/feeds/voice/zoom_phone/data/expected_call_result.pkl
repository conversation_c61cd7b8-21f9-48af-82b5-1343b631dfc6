���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�00:04:35��00:00:11��00:00:11��00:00:00��00:02:04��00:03:30��00:05:58��00:05:42��pandas._libs.missing��NA���h)�00:01:39��00:01:39�et�b�builtins��slice���K KK��R�K��R�hhhK ��h��R�(KKK��h�]�(�VOIP�h9h9h9h9�PSTN�h9h9h9h9h9h9et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(G�      G�      G�      G�      G�      �£0.2076�G�      G�      G�      G�      G�      G�      et�bh/KKK��R�K��R�h�numpy.core.numeric��_frombuffer���(�          �h�b1�����R�(KhNNNJ����J����K t�bKK���C�t�R�h/KKK��R�K��R�hhM(�           �hQKK��hTt�R�h/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�$c5213ec5-3c79-4221-b87d-2c4f37229537��$19130130-874b-4625-b759-ec243af712bd��$c16d4f40-fa27-4152-a329-cc3c4469bd87��$6f21d3ed-dfb3-4ecb-b29d-ddefc1111ba6��$3234c33e-5e7e-4c04-b97a-56b79fbd69d2��$93048b2d-ee8b-4b54-98ba-62fdbff7ba63��$e01b1c22-12c5-4cd8-b486-b754b99dcde8��$2e351a0f-874c-46db-98df-a639bd00be60��$dceb9d4d-d6b4-460b-8c64-2b1f7a5b1891��$b4ea4262-c43e-4750-a52a-88f83a32f94a��$1f8869fe-e4ad-493f-b636-5680df1c5e09��$8baa1a48-3cce-4990-8351-970e717cd4f2�et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(h)h)h)h)h)]��GB�a]�hah)h)h)h)h)et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(]�(�821��823�e]�(�832��847�e]�(�832��847�e]�(�821��822�e]�(�821��822�e]�(�
+442038215572��
+447703353476�e]�(�
+442038215572��821�e]�(�821��823�e]��821�a]��821�a]�(�821��823�e]�(�821��823�eet�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(h�h�h�h�h��
+442038215572�h�h�h�h�h�h�et�bh/KK	K��R�K��R�hhhK ��h��R�(KKK��h�]�(}�(�raw�h��countryCode�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�hu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nu}�(h�h�h�Nuet�bh/K	K
K��R�K��R�hhhK ��h��R�(KKK��h�]�(�Tom Scott (SteelEye)��Jaypal Sihra (SteelEye)��Jaypal Sihra (SteelEye)��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Mark Farmer��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Mark Farmer��Mark Farmer�et�bh/K
KK��R�K��R�hhhK ��h��R�(KKK��h�]�(]�h�a]�h�a]�h�a]�h�a]�h�a]��
+447703353476�a]��
+442038215572�a]�h�a]��821�a]��821�a]�h�a]�h�aet�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�hua]�}�(h�h�h�hua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nua]�}�(h�h�h�Nuaet�bh/KK
K��R�K��R�hhhK ��h��R�(KKK��h�]�(�Mark Farmer��Jonathan Omigie��Jonathan Omigie��Ben Lincoln��Ben Lincoln�G�      �Mark Farmer��Mark Farmer��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Tom Scott (SteelEye)��Tom Scott (SteelEye)�et�bh/K
KK��R�K��R�hhhK ��h��R�(KKK��h�]�(������������et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�
Zoom Phone�jK  jK  jK  jK  jK  jK  jK  jK  jK  jK  jK  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�pinafore.dev.steeleye.co�jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�Karies/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/call_logs.json�ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  ja  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(G�      G�      G�      G�      G�      �£0.0519�G�      G�      G�      G�      G�      G�      et�bh/KKK��R�K��R�hhM(�`                                                                       	       
              �h�i8�����R�(K�<�NNNJ����J����K t�bKK��hTt�R�h/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�is3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/call_logs.json�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�2022-06-28T09:17:08.000000Z��2022-06-28T09:11:58.000000Z��2022-06-28T09:11:58.000000Z��2022-06-28T09:09:29.000000Z��2022-06-28T09:11:33.000000Z��2022-06-28T09:12:33.000000Z�h)�2022-06-28T09:12:17.000000Z��2022-06-28T09:05:59.000000Z��2022-06-28T09:06:00.000000Z��2022-06-28T09:03:28.000000Z��2022-06-28T09:03:28.000000Z�et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�2022-06-28T09:12:27.000000Z��2022-06-28T09:11:38.000000Z��2022-06-28T09:11:38.000000Z��2022-06-28T09:08:58.000000Z��2022-06-28T09:08:57.000000Z��2022-06-28T09:08:56.000000Z��2022-06-28T09:06:32.000000Z��2022-06-28T09:06:31.000000Z��2022-06-28T09:05:46.000000Z��2022-06-28T09:05:46.000000Z��2022-06-28T09:01:44.000000Z��2022-06-28T09:01:44.000000Z�et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(j�  j�  j�  j�  j�  j�  h)j�  j�  j�  j�  j�  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�test.dev.steeleye.co�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh/KKK��R�K��R�hhhK ��h��R�(KKK��h�]�(�]attachments/waveform/zoom_phone_voice_flow/c5213ec5-3c79-4221-b87d-2c4f37229537/waveform.json��]attachments/waveform/zoom_phone_voice_flow/19130130-874b-4625-b759-ec243af712bd/waveform.json��]attachments/waveform/zoom_phone_voice_flow/c16d4f40-fa27-4152-a329-cc3c4469bd87/waveform.json��]attachments/waveform/zoom_phone_voice_flow/6f21d3ed-dfb3-4ecb-b29d-ddefc1111ba6/waveform.json��]attachments/waveform/zoom_phone_voice_flow/3234c33e-5e7e-4c04-b97a-56b79fbd69d2/waveform.json��]attachments/waveform/zoom_phone_voice_flow/93048b2d-ee8b-4b54-98ba-62fdbff7ba63/waveform.json��]attachments/waveform/zoom_phone_voice_flow/e01b1c22-12c5-4cd8-b486-b754b99dcde8/waveform.json��]attachments/waveform/zoom_phone_voice_flow/2e351a0f-874c-46db-98df-a639bd00be60/waveform.json��]attachments/waveform/zoom_phone_voice_flow/dceb9d4d-d6b4-460b-8c64-2b1f7a5b1891/waveform.json��]attachments/waveform/zoom_phone_voice_flow/b4ea4262-c43e-4750-a52a-88f83a32f94a/waveform.json��]attachments/waveform/zoom_phone_voice_flow/1f8869fe-e4ad-493f-b636-5680df1c5e09/waveform.json��]attachments/waveform/zoom_phone_voice_flow/8baa1a48-3cce-4990-8351-970e717cd4f2/waveform.json�et�bh/KKK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j�  �Index���}�(�data�hhK ��h��R�(KK��h�]�(�callDuration��callType��charge��	connected��
hasAttachment��id��identifiers.allCountryCodes��identifiers.allIds��identifiers.fromId��identifiers.fromIdAddlInfo��identifiers.fromUserId��identifiers.toIds��identifiers.toIdsAddlInfo��identifiers.toUserId��internal��metadata.source.client��(metadata.source.fileInfo.location.bucket��%metadata.source.fileInfo.location.key��rate��sourceIndex��	sourceKey��timestamps.localTimestampEnd��timestamps.localTimestampStart��timestamps.timestampEnd��timestamps.timestampStart��waveform.location.bucket��waveform.location.key�et�b�name�Nu��R�j�  �pandas.core.indexes.range��
RangeIndex���}�(j  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.