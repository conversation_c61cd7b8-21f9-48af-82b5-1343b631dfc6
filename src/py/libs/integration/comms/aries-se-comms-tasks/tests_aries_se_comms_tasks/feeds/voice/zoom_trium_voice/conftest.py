# ruff: noqa: E501
import pandas as pd
import pytest


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "date_time": [
                "GMT20211214-163047",
                "GMT20220124-161113",
                "GMT20220127-153211",
                "GMT20220511-140416",
            ],
            "from_id": [
                " <EMAIL>",
                " <EMAIL>",
                " <EMAIL>",
                " <EMAIL>",
            ],
            "call_id": ["864 4013 0133 ", "859 3677 2933 ", "839 1610 7333 ", "831 2275 0933 "],
            "call_name": [
                "OD _ M&G _ TH TIC",
                "D J & A L TIC",
                "Monthly L Global Macro Risk Meeting ",
                "J J _ S D _ M DiC",
            ],
            "file_url": [
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20211214-163047_Recording - <EMAIL> - 864 4013 0133 - OD _ M&G _ TH TIC.m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220124-161113_Recording - <EMAIL> - 859 3677 2912 - D J & A L TIC.m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220127-153211_Recording - <EMAIL> - 839 1610 7333 - Monthly L Global Macro Risk Meeting .m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220511-140416_Recording - <EMAIL> - 831 2275 0933 - J J _ S D _ M DiC.m4a",
            ],
            "__source_key__": [
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20211214-163047_Recording - <EMAIL> - 864 4013 0133 - OD _ M&G _ TH TIC.m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220124-161113_Recording - <EMAIL> - 859 3677 2912 - D J & A L TIC.m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220127-153211_Recording - <EMAIL> - 839 1610 7333 - Monthly L Global Macro Risk Meeting .m4a",
                "s3://irises8.sit.steeleye.co/aries/ingress/streamed/evented/zoom_trium_voice/GMT20220511-140416_Recording - <EMAIL> - 831 2275 0933 - J J _ S D _ M DiC.m4a",
            ],
        }
    )


@pytest.fixture()
def expected_result() -> pd.DataFrame:
    return pd.DataFrame(
        data=[
            {
                "callDuration": "1:20:30",
                "hasAttachment": True,
                "id": "864 4013 0133OD _ M&G _ TH TICGMT20211214-163047",
                "identifiers.allIds": ["<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": None,
                "identifiers.toIdsAddlInfo": None,
                "identifiers.allCountryCodes": None,
                "timestamps.localTimestampStart": "2021-12-14T16:30:47.000000Z",
                "timestamps.timestampStart": "2021-12-14T16:30:47.000000Z",
                "timestamps.localTimestampEnd": "2021-12-14T17:51:17.000000Z",
                "timestamps.timestampEnd": "2021-12-14T17:51:17.000000Z",
                "metadata.source.client": "Zoom",
                "metadata.source.fileInfo.location.bucket": "irises8.sit.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/evented/zoom_trium_voice/GMT20211214-163047_Recording - <EMAIL> - 864 4013 0133 - OD _ M&G _ TH TIC.m4a",
                "sourceIndex": 0,
                "sourceKey": "s3://test.dev.steeleye.co/batch_txt_url.ndjson",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/zoom_trium_voice_transform/864 4013 0133OD _ M&G _ TH TICGMT20211214-163047/waveform.json",
            },
            {
                "callDuration": "00:00:55",
                "hasAttachment": True,
                "id": "859 3677 2933D J & A L TICGMT20220124-161113",
                "identifiers.allIds": ["<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": None,
                "identifiers.toIdsAddlInfo": None,
                "identifiers.allCountryCodes": None,
                "timestamps.localTimestampStart": "2022-01-24T16:11:13.000000Z",
                "timestamps.timestampStart": "2022-01-24T16:11:13.000000Z",
                "timestamps.localTimestampEnd": "2022-01-24T16:12:08.000000Z",
                "timestamps.timestampEnd": "2022-01-24T16:12:08.000000Z",
                "metadata.source.client": "Zoom",
                "metadata.source.fileInfo.location.bucket": "irises8.sit.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/evented/zoom_trium_voice/GMT20220124-161113_Recording - <EMAIL> - 859 3677 2912 - D J & A L TIC.m4a",
                "sourceIndex": 1,
                "sourceKey": "s3://test.dev.steeleye.co/batch_txt_url.ndjson",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/zoom_trium_voice_transform/859 3677 2933D J & A L TICGMT20220124-161113/waveform.json",
            },
            {
                "callDuration": "00:00:23",
                "hasAttachment": True,
                "id": "839 1610 7333Monthly L Global Macro Risk MeetingGMT20220127-153211",
                "identifiers.allIds": ["<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": None,
                "identifiers.toIdsAddlInfo": None,
                "identifiers.allCountryCodes": None,
                "timestamps.localTimestampStart": "2022-01-27T15:32:11.000000Z",
                "timestamps.timestampStart": "2022-01-27T15:32:11.000000Z",
                "timestamps.localTimestampEnd": "2022-01-27T15:32:34.000000Z",
                "timestamps.timestampEnd": "2022-01-27T15:32:34.000000Z",
                "metadata.source.client": "Zoom",
                "metadata.source.fileInfo.location.bucket": "irises8.sit.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/evented/zoom_trium_voice/GMT20220127-153211_Recording - <EMAIL> - 839 1610 7333 - Monthly L Global Macro Risk Meeting .m4a",
                "sourceIndex": 2,
                "sourceKey": "s3://test.dev.steeleye.co/batch_txt_url.ndjson",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/zoom_trium_voice_transform/839 1610 7333Monthly L Global Macro Risk MeetingGMT20220127-153211/waveform.json",
            },
            {
                "callDuration": "00:00:42",
                "hasAttachment": True,
                "id": "831 2275 0933J J _ S D _ M DiCGMT20220511-140416",
                "identifiers.allIds": ["<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": None,
                "identifiers.toIdsAddlInfo": None,
                "identifiers.allCountryCodes": None,
                "timestamps.localTimestampStart": "2022-05-11T14:04:16.000000Z",
                "timestamps.timestampStart": "2022-05-11T14:04:16.000000Z",
                "timestamps.localTimestampEnd": "2022-05-11T14:04:58.000000Z",
                "timestamps.timestampEnd": "2022-05-11T14:04:58.000000Z",
                "metadata.source.client": "Zoom",
                "metadata.source.fileInfo.location.bucket": "irises8.sit.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/evented/zoom_trium_voice/GMT20220511-140416_Recording - <EMAIL> - 831 2275 0933 - J J _ S D _ M DiC.m4a",
                "sourceIndex": 3,
                "sourceKey": "s3://test.dev.steeleye.co/batch_txt_url.ndjson",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/zoom_trium_voice_transform/831 2275 0933J J _ S D _ M DiCGMT20220511-140416/waveform.json",
            },
        ]
    )
