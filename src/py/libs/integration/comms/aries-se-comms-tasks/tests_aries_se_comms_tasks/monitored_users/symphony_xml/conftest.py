import pandas as pd
import pytest
from se_elastic_schema.static.reference import ImAccountType


@pytest.fixture()
def monitored_users_from_elastic_mock() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "name": "<PERSON>",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "jkmho", "label": ImAccountType.BBG},
                    {"id": "jkmho", "label": ImAccountType.LIVE_CHAT},
                ],
                "uniqueIds": ["<EMAIL>", "jkmho"],
            },
            {
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "name": "<PERSON>",
                "communications.emails": pd.NA,
                "communications.imAccounts": [
                    {"id": "jim.doe", "label": ImAccountType.LIVE_CHAT.value},
                    {"id": "jim.doe", "label": ImAccountType.SYMPHONY.value},
                ],
                "uniqueIds": ["jim.doe"],
            },
            {
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "name": "Jack Doe",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": pd.NA,
                "uniqueIds": ["<EMAIL>"],
            },
            {
                "&id": "6293cf20-64ba-46eb-821f-04a41hb47371",
                "name": "Jill Doe",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "jIll.doe", "label": ImAccountType.BBG.value},
                ],
                "uniqueIds": ["<EMAIL>", "jill.doe"],
            },
        ]
    )


def participants_data():
    return [
        (
            [
                {
                    "types": ["FROM", "TO"],
                    "value": {
                        "&id": "sothi_id",
                        "&key": "AccountPerson:xyz",
                        "uniqueIds": ["<EMAIL>"],
                        "name": "Sothi",
                    },
                },
                {
                    "types": ["TO"],
                    "value": {
                        "&id": "rinaldi_id",
                        "&key": "AccountPerson:xyz",
                        "uniqueIds": ["<EMAIL>"],
                        "name": "Rinaldi",
                    },
                },
            ],
            True,
        ),
        (
            [
                {
                    "types": ["FROM", "TO"],
                    "value": {
                        "&id": "patrick_id",
                        "&key": "AccountPerson:xyz",
                        "name": "Patrick",
                        "uniqueIds": ["<EMAIL>"],
                    },
                }
            ],
            False,
        ),
    ]
