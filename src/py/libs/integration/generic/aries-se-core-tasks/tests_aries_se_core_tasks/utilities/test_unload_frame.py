import pandas as pd
from aries_se_core_tasks.utilities.unload_frame import unload_frame


class TestUnloadFrame:
    def test_unload_frame(self):
        """This test ensures:

        - If last frame is already the batch size, then the last frame is returned as is
        - The frame is split into batches up to the batch size
        - No data is lost during the batching process
        """
        df: pd.DataFrame = pd.DataFrame(
            data={
                "A": [1, 2, 3],
                "C": ["D1", "D2", "D3"],
            }
        )

        df_to_add: pd.DataFrame = pd.DataFrame(
            data={
                "A": [4, 5, 6, 7, 8],
                "C": ["D4", "D5", "D6", "D7", "D8"],
            }
        )

        result = unload_frame(df=df_to_add, previous_df=df, size=3)

        assert len(result) == 3

        pd.testing.assert_frame_equal(left=result[0].frame(), right=df)  # type: ignore[operator]

        pd.testing.assert_frame_equal(
            left=result[1].frame(),  # type: ignore[operator]
            right=df_to_add.iloc[:3],
        )

        pd.testing.assert_frame_equal(
            left=result[2].frame(),  # type: ignore[operator]
            right=df_to_add.iloc[3:].reset_index(drop=True),
        )

    def test_unload_frame_previous_is_not_full(self):
        """This test ensures:

        - If last frame is not full, then it will be filled up with the first
        rows of the new frame
        - The frame is split into batches up to the batch size
        - No data is lost during the batching process
        """
        df: pd.DataFrame = pd.DataFrame(
            data={
                "A": [1, 2, 3],
                "C": ["D1", "D2", "D3"],
            }
        )

        df_to_add: pd.DataFrame = pd.DataFrame(
            data={
                "A": [4, 5, 6, 7, 8],
                "C": ["D4", "D5", "D6", "D7", "D8"],
            }
        )

        result = unload_frame(df=df_to_add, previous_df=df, size=4)

        assert len(result) == 2

        pd.testing.assert_frame_equal(
            left=result[0].frame(),  # type: ignore[operator]
            right=pd.concat(
                [
                    df,
                    df_to_add.iloc[:1],
                ]
            ).reset_index(drop=True),
        )

        pd.testing.assert_frame_equal(
            left=result[1].frame(),  # type: ignore[operator]
            right=df_to_add.iloc[1:].reset_index(drop=True),
        )
