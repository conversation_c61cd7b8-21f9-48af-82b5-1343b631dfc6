from se_redis_utils import redis_utils


def test_cast_to_bool():
    # Testing with bool value, this should return the exact value passed
    val = redis_utils.cast_to_bool(value=True)
    assert val is True
    val = redis_utils.cast_to_bool(value=False)
    assert val is False

    # Testing with int value
    val = redis_utils.cast_to_bool(value=1)
    assert val is True
    val = redis_utils.cast_to_bool(value=0)
    assert val is False
    # Any digit > 1 should return True as well
    val = redis_utils.cast_to_bool(value=4)
    assert val is True

    # Testing with value set as "0" and "1"
    val = redis_utils.cast_to_bool(value="0")
    assert val is False
    val = redis_utils.cast_to_bool(value="1")
    assert val is True
    # Any digit > 1 should return True as well
    val = redis_utils.cast_to_bool(value="4")
    assert val is True

    # Testing with value set as "true" and "false"
    val = redis_utils.cast_to_bool(value="false")
    assert val is False
    val = redis_utils.cast_to_bool(value="true")
    assert val is True
    # Any other invalid value passed should return False
    val = redis_utils.cast_to_bool(value="xyz")
    assert val is False
