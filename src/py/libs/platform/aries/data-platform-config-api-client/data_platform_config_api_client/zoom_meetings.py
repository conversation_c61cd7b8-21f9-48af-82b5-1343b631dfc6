import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser
from data_platform_config_api_client.base import DataPlatformConfigAPI
from typing import ClassVar, Set

log = logging.getLogger(__name__)


class ZoomMeetingsAPI(DataPlatformConfigAPI):
    PREFIX: ClassVar[str] = "/stacks/{stack_name}/tenants/{tenant_name}/workflows/zoom_meetings"
    CONFIG: ClassVar[str] = "/config"

    def get_prefix(self) -> str:
        return super().get_prefix() + ZoomMeetingsAPI.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._get_config: EndPoint[Set[str]] = EndPoint(
            path=ZoomMeetingsAPI.CONFIG,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )

        self._upsert_config_for_tenant: EndPoint[Set[str]] = EndPoint(
            path=ZoomMeetingsAPI.CONFIG,
            http_verb="PUT",
            response_parser=AddictResponseParser(),
        )

    def get_config(
        self,
        stack_name: str,
        tenant_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._get_config,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
            },
        )

    def upsert_config_for_tenant(
        self,
        json_body: dict[str, str],
        stack_name: str,
        tenant_name: str,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._upsert_config_for_tenant,
            json_body=json_body,
            path_params={
                "stack_name": stack_name,
                "tenant_name": tenant_name,
            },
        )


class ZoomMeetingsConfigAPI(DataPlatformConfigAPI):
    PREFIX: ClassVar[str] = "/tenant_workflows/zoom_meetings/config"
    CONFIG: ClassVar[str] = ""

    def get_prefix(self) -> str:
        return super().get_prefix() + ZoomMeetingsConfigAPI.PREFIX

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client

        self._search_config: EndPoint[Set[str]] = EndPoint(
            path=ZoomMeetingsConfigAPI.CONFIG,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )

    def search_config(
        self,
        account_id: str | None = None,
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._search_config,
            query_param={"account_id": account_id},
        )
