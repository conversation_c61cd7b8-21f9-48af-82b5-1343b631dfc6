# ruff: noqa: E501
import logging
from efdh_api.utils.efdh_config import EFDHConfig
from efdh_api.utils.es_dsl.base import QueryModelBase
from efdh_api.utils.exceptions import MoreThanOneRecordError
from efdh_api.utils.results import Pagination, RawResult
from efdh_api.utils.utils import nested_get
from elasticsearch8 import Elasticsearch
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from typing import Dict, List, Optional, Type, Union


def to_dict_deserializer(record):
    return record["_source"]


# TODO This should be replaced with se_schema's TenantConfiguration when RequestBoundRepository is ready for it.
# class TenantConfiguration(RecordModel):
#     customerId: Optional[str] = None
#     emailDomains: Optional[List[str]] = None
#     featureFlags: Optional[List[str]] = None
#     loginLandingPage: Optional[str] = None
#     maxInvalidLoginAttempts: Optional[int] = 3
#     ssoCognitoPoolId: Optional[str] = None
#     ssoUrl: Optional[str] = None
#     subscribedMarketAbuseReports: Optional[List[MarketAbuseReportType]] = None
#     subscribedModules: Optional[List[Module]] = None
#     tenantId: Optional[str] = None
#     trPIEnrichmentEnabled: Optional[bool] = None
#     trSchedule: Optional[RTS22TransactionReportSchedule] = None
#     trV2ModulePermissions: Optional[RTS22TransactionReportingModulePermissions] = None
#     usageTrackingEnabled: Optional[bool] = None
#     userIdleTimeoutMs: Optional[int] = 900000
#     customResolutionCategories: Optional[CustomResolutionCategories] = None
#
#     class Config:
#         model_name = "TenantConfiguration"
#         index_suffix = "tenant_configuration"
#         fqn = "tenant/configuration"
#
#         extra = pydantic.Extra.allow
#
#     def has_feature_flag(self, f: Union[str, List[str]]):
#         if not isinstance(f, (list, tuple)):
#             f = [f]
#         all_flags = self.featureFlags or []
#         return all(_f in all_flags for _f in f)


log = logging.getLogger(__name__)


# @functools.lru_cache(maxsize=1000)
# def _get_rm_config(rm_type) -> RecordModelConfigProtocol:
#     return compat2.config_of(rm_type)


# def _do_deserialize(rm_type: Type[SteelEyeSchemaBaseModelES8], record: dict, validate: bool):
#     rm_config = _get_rm_config(rm_type)
#
#     construct_args = record["_source"]
#
#     if rm_config.parse_inner_hits and record.get("inner_hits"):
#         inner_hit_type = rm_config.inner_hit_type
#         inner_hit_records = record["inner_hits"].get(inner_hit_type).get("hits")
#         if inner_hit_records and inner_hit_records.get("total"):
#             construct_args[rm_config.inner_hit_property_name] = inner_hit_records["hits"][0].get(
#                 "_source"
#             )
#
#     if validate:
#         return rm_type(**construct_args)
#     else:
#         return rm_type.construct(**construct_args)


# class HookExecution:
#     """Cache TenantConfigurationOut for use in before-save and after-save
#     hooks."""
#
#     def __init__(self, repo: "BoundRepository", record: RecordModel, action: ChangeAction):
#         self.repo = repo
#         self.record = record
#         self.action = action
#         self._tenant_configuration = None
#         self._kafka_rest_proxy_client = KafkaRestClient(
#             kafka_rest_proxy_url=self.repo.api_config.KAFKA_REST_PROXY_URL,
#         )
#
#     @property
#     def tenant_configuration(self) -> TenantConfiguration:
#         if self._tenant_configuration is None:
#             self._tenant_configuration = self.repo.s_get_tenant_configuration()
#         return self._tenant_configuration
#
#     @property
#     def should_run(self):
#         # Do not process audit records themselves
#         return self.record.__class__.__name__ != CascadeAudit.__name__
#
#     def call_before_save(self):
#         if not self.should_run:
#             return
#
#         kwargs = {}
#         if self.record.__config__.save_hooks_require_tenant_configuration:
#             kwargs = {"tenant_configuration": self.tenant_configuration}
#         if self.action == ChangeAction.CREATED:
#             self.record.before_save_new(**kwargs)
#         elif self.action == ChangeAction.UPDATED:
#             self.record.before_save_existing(**kwargs)
#
#     async def notify_change(self, saved_record: Dict, **kwargs):
#         if not self.should_run:
#             return
#
#         changes = []
#         for change_factory in get_change_factories(model=self.record.__class__, action=self.action):
#             changes.extend(
#                 change_factory(
#                     realm=self.repo.realm,
#                     model=self.record.__class__.__name__,
#                     action=self.action,
#                     source=saved_record,
#                     user_id=self.repo.tenancy.userId,
#                 )
#             )
#
#         if not changes:
#             return
#
#         # Kafka message to cascade should only be sent when
#         # cSurv ot TRv2 modules are enabled for the tenant.
#
#         tenant_config = self.tenant_configuration
#         cascade_modules = [Module.COMMS_SURVEILLANCE, Module.TRANSACTION_REPORTING_V2]
#         allow_cascade = any(module in tenant_config.subscribedModules for module in cascade_modules)
#
#         if self.tenant_configuration.has_feature_flag("disableCascade") or not allow_cascade:
#             log.info(f"Skipping as cascade disabled for realm {self.repo.realm}")
#             return
#
#         for change in changes:
#             audit = CascadeAudit(
#                 cascadeId=change.cascadeId,
#                 queued=utc_timestamp_millis(),
#                 action=change.action.value,
#                 realm=change.realm,
#                 sourceModel=change.sourceModel,
#                 sourceId=change.sourceId,
#                 sourceTimestamp=change.sourceTimestamp,
#                 status=TaskStatus.QUEUED,
#                 triggeredBy=change.triggeredBy,
#                 assignedIdentifier=kwargs.get("assignedIdentifier"),
#             )
#             try:
#                 # IMPORTANT: DO NOT CHANGE THE LOGIC for MessageGroupId. It's used to acheive the
#                 # PAUSE functionality in CASCADE router. If any change is being done, please liaise
#                 # the message to Data Platform Team first.
#                 topic = f"aries.cascade.{self.repo.api_config.ENVIRONMENT}.{self.repo.tenancy.tenant}.events"
#                 workflow = WorkflowFieldSet(
#                     name="cascade",
#                     stack=self.repo.api_config.ENVIRONMENT,
#                     tenant=self.repo.tenancy.tenant,
#                     start_timestamp=datetime.utcnow(),
#                 )
#                 task = TaskFieldSet(
#                     name=self.repo.api_config.API_SERVICE_NAME,
#                     version=self.repo.api_config.API_VERSION,
#                     success=True,
#                 )
#                 io_param = IOParamFieldSet(params=change.dict())
#                 io_event = IOEvent(workflow=workflow, io_param=io_param, task=task)
#                 res = self._kafka_rest_proxy_client.send(
#                     io_event=io_event,
#                     topic=topic,
#                     raise_on_connection_error=True,
#                     raise_on_serder_error=True,
#                 )
#                 log.info(
#                     f"Queued cascade {change.cascadeId} for realm {self.repo.realm}, res: {res}"
#                 )
#                 log.info(f"Here is the cascade event: {io_event.dict(exclude_none=True)}")
#             except Exception as e:
#                 audit.status = TaskStatus.ERRORED
#                 audit.taskError = f"API failed to send cascade message. Reason: {e}"
#                 log.error(
#                     f"Failed to queue cascade {change.cascadeId} for realm {self.repo.realm}: {e}"
#                 )
#                 log.exception(traceback.format_exc())
#
#             await self.repo.save_new(audit)


class EFDHRepository:
    # skip_policy: bool  # Option to skip apply policy

    def __init__(self, config: EFDHConfig, es_client: Elasticsearch):
        self._config = config
        self._es_client = es_client

    def get_response_model_map(self, se_models: List[Type[SteelEyeSchemaBaseModelES8]]):
        """Creates a mapping from model names to their corresponding SteelEyeSchemaBaseModelES8 classes."""
        return {se_model.get_reference().name: se_model for se_model in se_models}

    def get_index_aliases(self, se_models: List[Type[SteelEyeSchemaBaseModelES8]]) -> str:
        """Generates a comma-separated string of index aliases for the given SteelEyeSchemaBaseModelES8 classes."""
        return ",".join(
            se_model.get_elastic_index_alias(tenant=self._config.efdh_tenant)
            for se_model in se_models
        )

    def search(
        self,
        *,
        query_body: dict,
        se_models: List[Type[SteelEyeSchemaBaseModelES8]],
        parse_to_model: bool = True,
    ) -> RawResult:
        index_alias = self.get_index_aliases(se_models)
        response_model_map = self.get_response_model_map(se_models)

        log.debug("Running search against %s with query %s", index_alias, query_body)
        raw_results = RawResult.parse_obj(
            self._es_client.search(index=index_alias, body=query_body)
        )
        raw_results.set_last_sort()
        raw_results.parse_hits_to_response(response_model_map, parse_to_model=parse_to_model)
        return raw_results

    def alias_exists(self, alias: str):
        return self._es_client.indices.exists_alias(name=alias)

    def filter_aliases(self, aliases: List[str]) -> List[str]:
        if isinstance(aliases, list):
            return [alias for alias in aliases if self.alias_exists(alias)]
        return aliases

    def execute_search(
        self,
        *,
        query_model: QueryModelBase,
        se_models: List[Type[SteelEyeSchemaBaseModelES8]],
        count=False,
        parse_to_model: bool = True,
    ) -> RawResult:
        query_body = query_model.to_dict(count=count)
        index_alias = ",".join(
            se_model.get_elastic_index_alias(tenant=self._config.efdh_tenant)
            for se_model in se_models
        )

        if self._config.debug:
            log.debug("Against %s, executing query: %s", index_alias, query_body)

        handle = {}

        if count:
            handle.update(isCount=True)
            try:
                result = self._es_client.count(body=query_body, index=index_alias)["count"]
            except Exception as e:
                handle.update(queryError=repr(e))
                raise

            handle.update(isOk=True)
            return result

        else:
            try:
                result = RawResult.parse_obj(
                    self._es_client.search(body=query_body, index=index_alias)
                )
            except Exception as e:
                handle.update(queryError=repr(e))
                raise

            handle.update(
                isOk=True,
                took=result.took,
                hitsTotal=result.hits.total.value,
                hitsReturned=len(result.hits.hits),
            )
            result.set_last_sort()
            result.parse_hits_to_response(
                response_model_map=self.get_response_model_map(se_models),
                parse_to_model=parse_to_model,
            )
            return result

    # TODO: because there is no usage in EFDH
    # def execute_scan(
    #         self,
    #         query_model: QueryModelBase,
    #         *,
    #         index_suffix: str = None,
    #         index: Union[str, List[str]] = None,
    #         hit_deserializer: Callable = None,
    #         size: int = None,
    # ):
    #     if index is None:
    #         index = self.index_for(index_suffix)
    #
    #     index = self.filter_aliases(index)
    #
    #     # TODO: this needs to be tested and have proper error handling!!!
    #     # if self._policy_result and self._policy_result.policy_filter:
    #     #     query_model.apply_policy(policy_filter=PolicyFilter(policy_result=self._policy_result))
    #
    #     query = query_model.build(index=index, paginate=False, size=size).to_dict()
    #     log.info(f"Against {index}, executing query for scroll: {json.dumps(query, default=str)}")
    #     for hit in self.es_repo.scan(index=index, query=query):
    #         if hit_deserializer:
    #             hit = hit_deserializer(hit)
    #         yield hit

    def get_many(
        self,
        *,
        query_model: QueryModelBase,
        se_models: List[Type[SteelEyeSchemaBaseModelES8]],
        # where: Optional[Iterable[Featurable]] = None,
        # terms: Dict[str, Union[str, List[str]]] = None,
        # validate=True,
        pagination: Pagination = Pagination(),
        count=False,  # Not interested in results, aggregations or pagination, just the count
        # hit_deserializer=NOT_SET,
        # Override the default record_model based deserializer. Pass None to skip.
        aggs: Dict = None,
        # use_reference: bool = False,
        track_total_hits: Optional[Union[int, bool]] = True,
        parse_to_model: bool = True,
    ) -> RawResult:
        """If query_model is specified, most of the search related arguments
        are ignored as they should have been injected into the query_model
        already.

        If "where" is passed, a query_model_cls is constructed on the fly using query_model_cls
        as base class. It is incompatible with "query_model".
        If "where" is used, there should be no parameters - they should have been passed to features
        already. Then **kwargs are passed to the dictionary of search model
        class being created on the fly.

        "index" should be specified only if the automatic index picking
        from record_model based on Config.index_suffix cannot be relied upon.
        """

        if aggs:
            for name, agg in aggs.items():
                query_model.agg(name, agg)

        return self.execute_search(
            query_model=query_model, se_models=se_models, count=count, parse_to_model=parse_to_model
        )

    def get_count(self, *args, **kwargs):
        """Accepts same args and kwargs as get_many but returns just the
        response to the count query."""
        kwargs["count"] = True
        return self.get_many(*args, **kwargs)

    def get_aggs(
        self,
        query_model: QueryModelBase,
        se_models: List[Type[SteelEyeSchemaBaseModelES8]],
        aggs: Dict = None,
        parse_to_model: bool = True,
    ):
        """Accepts same args and kwargs as get_many, but automatically sets
        Pagination(take=0) as customary with aggregations."""
        query_model.params.pagination = Pagination(take=0)
        return self.get_many(
            query_model=query_model, se_models=se_models, aggs=aggs, parse_to_model=parse_to_model
        )

    def get_one(
        self,
        *,
        query_model: QueryModelBase,
        se_models: List[Type[SteelEyeSchemaBaseModelES8]],
        parse_to_model: bool = True,
    ) -> RawResult:
        """Retrieve single record of the specified type. The filters should be
        such that only one record matches the criteria. If more than one record
        is returned, this will raise a MoreThanOneRecordError. If no record is
        found, will raise a NotFound.

        "return_model" is needed only if it differs from "record_model".

        Set validate to False if the underlying data does not match the model schema.

        "index" should be specified only if the automatic index picking
        from record_model based on Config.index_suffix cannot be relied upon.

        If timestamp is specified and the returned record has a different timestamp
        this will raise an EditConflict exception.
        """

        raw_result = self.execute_search(
            query_model=query_model, se_models=se_models, parse_to_model=parse_to_model
        )

        if raw_result.hits.total.value == 0:
            # log.warning(f"Record not found: {se_model}, {id}")
            raise ValueError("No records found")
        if raw_result.hits.total.value > 1:
            raise MoreThanOneRecordError("More than one result was returned by the query")

        return raw_result

    def get_min_value(self, *, field: str, as_string: bool = False, **kwargs):
        aggregation = self.get_aggs(
            aggs={"MIN_VALUE": {"min": {"field": field}}},
            **kwargs,
        ).aggregations

        return (
            nested_get(
                aggregation,
                "MIN_VALUE.value_as_string" if as_string else "MIN_VALUE.value",
                default=None,
            )
            or None
        )
