import csv
import pycountry
from se_api_svc.services.kc_sync.constants import jurisdiction_fields, jurisdiction_fields_len
from se_elastic_schema.static.reference import ProfileLanguage


def jurisdiction_validator(value: str):
    jurisdiction_attrs = list(csv.reader([value or ""]))[0]
    if len(jurisdiction_attrs) != jurisdiction_fields_len:
        return (
            f"Jurisdiction attributes do not match: "
            f"found values '{jurisdiction_attrs}', required '{jurisdiction_fields}'"
        )

    # Second field should be valid country code
    if country_validator(jurisdiction_attrs[1]) is not None:
        return f"Invalid country code in jurisdiction '{jurisdiction_attrs[1]}'"


def language_validator(value: str):
    """
    Validate the language attribute.
    :param value: The language value from JWT payload.
    :return: None if valid, error message if invalid.
    """
    if value and value not in ProfileLanguage.list():
        return f"Invalid language '{value}', must be one of {ProfileLanguage.list()}"


def country_validator(value: str):
    """
    Validate the country attribute.
    :param value: The country value from JWT payload.
    :return: None if valid, error message if invalid.
    """
    return None if pycountry.countries.get(alpha_2=value) else f"Invalid country code '{value}'"
