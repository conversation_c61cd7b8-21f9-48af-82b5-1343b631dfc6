import logging
from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from api_sdk.exception_handlers import master_data_exception_handler
from api_sdk.exceptions import BadInput, NotFound
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.middleware.module_permission_checker import ModulePermissionChe<PERSON>
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.responses import OkResponse
from api_sdk.schemas.module_permission_checker import All, Any
from api_sdk.schemas.static import Module
from api_sdk.services.master_data import MasterDataClient
from api_sdk.utils.intervals import TimeInterval
from api_sdk.utils.utils import nested_get
from fastapi import APIRouter, Body, Depends, Query, Request
from se_api_svc.api.routes.surveillance.trades.query_params import TrendParams
from se_api_svc.messages.surveillance.commands import AlertsLabelsUpdateCommand
from se_api_svc.messages.surveillance.events import AlertsSearched, AlertViewed
from se_api_svc.repository.order.orders import OrdersRepository
from se_api_svc.repository.surveillance.alerts import AlertsRepository
from se_api_svc.schemas.surveillance.watches import (
    TradeSurveillanceQueryType,
    TradeSurveillanceTrendChart,
)
from se_api_svc.schemas.surveillance.workflow import AlertsLabelsBulkUpdate
from se_api_svc.schemas.track import ModuleTitle
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elastic_schema.static.surveillance import AlertHitStatus
from se_elastic_schema.static.tenant import ResolutionCategoryTypeVisibility
from typing import List, Optional

router = APIRouter()
log = logging.getLogger(__name__)


@router.get(
    "",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_alerts(
    params: PaginatedDatedListParams = Depends(),
    trend_params: TrendParams = Depends(),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    unresolved_alerts_assignee_status: Optional[List[str]] = Query(
        None, alias="unresolvedAlertsAssigneeStatus"
    ),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    search_mar_and_non_mar: bool = Query(False, alias="searchMarAndNonMar"),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    mb: FastApiMessageBus = Depends(),
):
    raw_result = await repo.get_alerts(
        module=ModuleTitle.TRADE_SURVEILLANCE,
        alert_resolution_category=alert_resolution_category,
        alert_resolution_sub_category=alert_resolution_sub_category,
        alert_status=alert_status,
        unresolved_alerts_assignee_status=unresolved_alerts_assignee_status,
        search_mar_and_non_mar=search_mar_and_non_mar,
        **trend_params.as_search_kwargs(),
        **params.as_search_kwargs(as_model_qs=False),
    )

    await mb.publish(
        AlertsSearched(
            search_text=params.search,
            refine_query=params.f,
            request=params.request,
        )
    )

    return SearchResult.from_raw_result(raw_result, skipped_hits=params.page_params.skip)


@router.get(
    "/summary/by-status",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_alerts_summary_by_status(
    params: NonPaginatedDatedListParams = Depends(),
    trend_params: TrendParams = Depends(),
    person: Optional[List[str]] = Query(None),
    watch_name: Optional[List[str]] = Query(alias="watchName", default=None),
    watch_type: Optional[TradeSurveillanceQueryType] = Query(alias="watchType", default=None),
    withSubStatuses: Optional[bool] = False,
    resolved_category_only: bool = Query(alias="resolvedCategoryOnly", default=False),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    return await repo.get_alerts_summary_by_status(
        module=ModuleTitle.TRADE_SURVEILLANCE,
        person=person,
        watch_name=watch_name,
        watch_type=watch_type,
        with_sub_statuses=withSubStatuses,
        resolved_category_only=resolved_category_only,
        **trend_params.as_search_kwargs(),
        **params.as_search_kwargs(),
    )


@router.get(
    "/summary/by-people",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_trades_alerts_summary_by_people(
    params: NonPaginatedDatedListParams = Depends(),
    watch_name: Optional[List[str]] = Query(alias="watchName", default=None),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    return sorted(
        await repo.get_alerts_summary_by_people(
            module=ModuleTitle.TRADE_SURVEILLANCE,
            watch_name=watch_name,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            alert_status=alert_status,
            **params.as_search_kwargs(),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get(
    "/summary/by-trend/{trend_chart}",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_trades_alerts_summary_by_trend(
    trend_chart: TradeSurveillanceTrendChart,
    params: NonPaginatedDatedListParams = Depends(),
    watch_name: Optional[List[str]] = Query(alias="watchName", default=None),
    person: Optional[List[str]] = Query(None),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    tenant_config = await repo.get_tenant_configuration()
    include_custom = (
        False
        if tenant_config.customResolutionCategories
        and tenant_config.customResolutionCategories.tSurvVisibility
        == ResolutionCategoryTypeVisibility.STEELEYE
        else True
    )

    return sorted(
        await repo.get_alerts_summary_by_trend(
            module=ModuleTitle.TRADE_SURVEILLANCE,
            trend_chart=trend_chart.value,
            watch_name=watch_name,
            person=person,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            alert_status=alert_status,
            include_custom=include_custom,
            **params.as_search_kwargs(as_model_qs=False),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get(
    "/summary/by-watch",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_trades_alerts_summary_by_watch(
    params: NonPaginatedDatedListParams = Depends(),
    trend_params: TrendParams = Depends(),
    person: Optional[List[str]] = Query(None),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    take: Optional[int] = Query(10, ge=1, le=250),
    repo: AlertsRepository = ReqDep(AlertsRepository),
):
    return sorted(
        await repo.get_alerts_summary_by_watch(
            module=ModuleTitle.TRADE_SURVEILLANCE,
            person=person,
            alert_resolution_category=alert_resolution_category,
            alert_resolution_sub_category=alert_resolution_sub_category,
            alert_status=alert_status,
            **trend_params.as_search_kwargs(),
            **params.as_search_kwargs(as_model_qs=False),
        ),
        key=lambda r: r["total"],
        reverse=True,
    )[:take]


@router.get(
    "/summary/by-time",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
async def get_trade_alerts_summary_by_time(
    params: NonPaginatedDatedListParams = Depends(),
    trend_params: TrendParams = Depends(),
    person: Optional[List[str]] = Query(None),
    alert_resolution_category: Optional[List[str]] = Query(None, alias="resolutionCategory"),
    alert_resolution_sub_category: Optional[List[str]] = Query(None, alias="resolutionSubCategory"),
    watch_name: Optional[List[str]] = Query(alias="watchName", default=None),
    alert_status: Optional[List[AlertHitStatus]] = Query(alias="alertStatus", default=None),
    repo: AlertsRepository = ReqDep(AlertsRepository),
    interval: Optional[TimeInterval] = None,
):
    return await repo.get_alerts_timeline(
        module=ModuleTitle.TRADE_SURVEILLANCE,
        interval=interval,
        person=person,
        watch_name=watch_name,
        alert_resolution_category=alert_resolution_category,
        alert_resolution_sub_category=alert_resolution_sub_category,
        alert_status=alert_status,
        **trend_params.as_search_kwargs(),
        **params.as_search_kwargs(as_model_qs=False),
    )


@router.get(
    "/{alert_id}",
    dependencies=[
        Depends(ModulePermissionChecker(Any(Module.COMMS_SURVEILLANCE, Module.TRADE_SURVEILLANCE)))
    ],
)
@master_data_exception_handler
async def get_trade_alert(
    alert_id: str,
    request: Request,
    repo: AlertsRepository = ReqDep(AlertsRepository),
    ord_repo: OrdersRepository = ReqDep(OrdersRepository),
    market_data_client: MasterDataClient = ReqDep(MasterDataClient),
    mb: FastApiMessageBus = Depends(),
):
    alert = await repo.get_alert(alert_id)

    order_id = alert.hit.get("&id")

    is_order_hit = nested_get(alert.hit, "executionDetails.orderStatus") == OrderStatus.NEWO
    try:
        if not is_order_hit:
            # to get aggregated order data, order_id should be the id of Order model
            order_state = await ord_repo.get_order_or_execution(order_id)
            order_id = order_state.parent_

        await mb.publish(AlertViewed(request=request, alert=alert))

        if order_id:
            stats = (await ord_repo.get_aggregated_order_data(order_id_codes=[order_id])).get(
                order_id, {}
            )
            alert["hit"]["statsDetails"] = stats.get("statsDetails")
    except NotFound:
        log.info(f"{order_id} not found in elastic")

    order_data = alert["hit"]
    instrument_id = (
        order_data.get("instrumentDetails", {})
        .get("instrument", {})
        .get("ext", {})
        .get("instrumentUniqueIdentifier")
    )

    order_submission = order_data.get("timestamps", {}).get("orderSubmitted")
    # fallback to get orderSubmitted field
    if not order_submission:
        # order may be deleted
        try:
            order = await ord_repo.get_order_or_execution(order_id)
            order_submission = order.timestamps.orderSubmitted
        except NotFound:
            order_submission = None

    if order_submission:
        day_volume = market_data_client.get_day_trading_volume(
            instrument_id=instrument_id, date_=order_submission
        )

        if day_volume != 0 and day_volume is not None:
            if alert["hit"]["statsDetails"] is None:
                alert["hit"]["statsDetails"] = {}
            alert["hit"]["statsDetails"]["dayTradingVolume"] = day_volume
            quantity = float(order_data.get("priceFormingData", {}).get("initialQuantity", 0))
            alert["hit"]["statsDetails"]["percentDayTradingVolume"] = quantity / day_volume

    return alert


@router.put(
    "/labels/bulk-update",
    name="surveillance:alerts:labels:bulk-update",
    dependencies=[
        Depends(ModulePermissionChecker(All(Module.TRADE_SURVEILLANCE))),
    ],
)
async def labels_bulk_update(
    bulk_request: AlertsLabelsBulkUpdate = Body(...),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    if not (bulk_request.alert_ids or bulk_request.scenario_ids):
        raise BadInput(msg="alertIds and scenarioIds empty", loc=["body", "ids"])

    if not bulk_request.update:
        raise BadInput(msg="field empty", loc=["body", "labelsUpdate"])

    await mb.publish(
        AlertsLabelsUpdateCommand(
            alert_ids=bulk_request.alert_ids,
            scenario_ids=bulk_request.scenario_ids,
            update=bulk_request.update,
        )
    )
    return OkResponse(updated=True, labels=bulk_request.update.as_dict())
