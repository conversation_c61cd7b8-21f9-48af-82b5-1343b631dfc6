import logging
from api_sdk.di.request import ReqDep
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import (
    CustomPaginatedDatedListParams,
    NonPaginatedDatedListParams,
)
from api_sdk.models.search import SearchResult
from api_sdk.responses import OkResponse
from fastapi import APIRouter, Body, Depends, Query
from se_api_svc.repository.surveillance.lexicon import LexiconRepository
from se_elastic_schema.components.surveillance.surveillance_lexicon import SurveillanceLexicon
from typing import List, Optional

log = logging.getLogger(__name__)

router = APIRouter()


def ensure_permissions(repo):
    """Will raise a 400 error if the user doesn't have sureveillance perms."""
    repo.tenancy.require_permissions(
        Permission.COMMS_SURVEILLANCE,
    )


@router.get("/categories", name="surveillance:lexicon:get-categories")
async def get_categories(
    params: NonPaginatedDatedListParams = Depends(),
    repo: LexiconRepository = ReqDep(LexiconRepository),
):
    ensure_permissions(repo)

    return await repo.get_categories(**params.as_search_kwargs())


@router.get("/lexicons", name="surveillance:lexicon:get-lexicons")
async def get_lexicons(
    categories: Optional[List[str]] = Query(None),
    params: CustomPaginatedDatedListParams = Depends(),
    repo: LexiconRepository = ReqDep(LexiconRepository),
):
    ensure_permissions(repo)

    return SearchResult.from_raw_result(
        (await repo.get_lexicons(categories=categories, **params.as_search_kwargs())),
        skipped_hits=params.page_params.skip,
    )


@router.get("/{lexicon_id}", name="surveillance:lexicon:get-one")
async def get_lexicon(lexicon_id, repo: LexiconRepository = ReqDep(LexiconRepository)):
    ensure_permissions(repo)

    lex = await repo.get_lexicon(lexicon_id)
    return lex


@router.post("", name="surveillance:lexicon:create")
async def create_lexicon(
    lexicon: SurveillanceLexicon = Body(None), repo: LexiconRepository = ReqDep(LexiconRepository)
):
    ensure_permissions(repo)

    await repo.save_new(lexicon)

    return lexicon


@router.delete("/{lexicon_id}", name="surveillance:lexicon:delete")
async def delete_lexicon(lexicon_id, repo: LexiconRepository = ReqDep(LexiconRepository)):
    ensure_permissions(repo)

    lex = await repo.get_lexicon(lexicon_id)
    await repo.delete_existing(lex)

    return OkResponse()


@router.put("/{lexicon_id}", name="surveillance:lexicon:update")
async def update_lexicon(
    lexicon_id,
    data: SurveillanceLexicon = Body(None),
    repo: LexiconRepository = ReqDep(LexiconRepository),
):
    ensure_permissions(repo)

    lex = (await repo.get_lexicon(lexicon_id)).copy(update=data.dict(by_alias=True))
    await repo.save_existing(lex)

    return OkResponse()
