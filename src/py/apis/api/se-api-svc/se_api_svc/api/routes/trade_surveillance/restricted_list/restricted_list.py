# type: ignore
import logging
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import EditConflict
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from fastapi import APIRouter, Depends, File, Query, Response, UploadFile, status
from pathlib import Path
from se_api_svc.messages.trade_surveillance.restricted_list import restricted_list_events as events
from se_api_svc.repository.trade_surveillance.restricted_list.sink_file import (
    SinkFileAuditRepository,
)
from se_api_svc.schemas.trade_surveillance.restricted_list.common import AttachmentIn, AttachmentOut
from se_api_svc.schemas.trade_surveillance.restricted_list.restricted_list_schema import (
    OptionalRestrictedListIn,
    RestrictedListIn,
    RestrictedListOut,
    RestrictedListOutPaginatedResponse,
)
from se_api_svc.schemas.trade_surveillance.restricted_list.restricted_list_upload import (
    FileExtension,
    RestrictedListUploadResponse,
)
from se_api_svc.services.trade_surveillance.restricted_list.restricted_list import (
    RestrictedListService,
)
from se_api_svc.services.trade_surveillance.restricted_list.restricted_list_upload import (
    RestrictedListUploadService as RLUploadService,
)
from se_api_svc.services.trade_surveillance.restricted_list.restriction import RestrictionService
from se_schema.all_enums import WatchStatusType
from starlette.requests import Request
from typing import Dict, Optional
from uuid import UUID

from se_api_svc.services.trade_surveillance.restricted_list.restricted_list_upload import (  # isort: skip
    RestrictedListUploadService as service,
)
from api_sdk.utils.pending_request import PendingBody

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post(
    path="",
    response_model=RestrictedListOut,
    response_model_by_alias=False,
    status_code=status.HTTP_201_CREATED,
)
async def create_restricted_list(
    request: Request,
    data: RestrictedListIn = Depends(PendingBody(RestrictedListIn)),
    service: RestrictedListService = ReqDep(RestrictedListService),
    mb: FastApiMessageBus = Depends(),
):
    restricted_list = await service.create_restricted_list(restricted_list_in=data)

    await mb.publish(events.RestrictedListCreated(record_obj=restricted_list, request=request))
    return restricted_list


@router.get(
    path="",
    response_model=RestrictedListOutPaginatedResponse,
    response_model_by_alias=False,
)
async def get_restricted_lists(
    params: PaginatedDatedListParams = Depends(),
    with_active_watches: Optional[bool] = Query(
        True, alias="live", description="Includes only lists with active watches when set to True"
    ),
    service: RestrictedListService = ReqDep(RestrictedListService),
    mb: FastApiMessageBus = Depends(),
):
    """Returns a list of restricted lists without including attachments of
    lists."""
    response = await service.get_restricted_lists(
        with_active_watches=with_active_watches,
        status=WatchStatusType.ACTIVE,
        **params.as_search_kwargs(),
    )

    await mb.publish(
        events.RestrictedListsSearched(
            search_text=params.search,
            refine_query=params.f,
            request=params.request,
        )
    )
    return response


@router.post(
    path="/{restricted_list_id}/ingest-file",
    status_code=status.HTTP_202_ACCEPTED,
)
async def ingest_restricted_list(
    restricted_list_id: UUID,
    response: Response,
    file: UploadFile = File(...),
    service: RestrictedListService = ReqDep(RestrictedListService),
    restriction_service: RestrictionService = ReqDep(RestrictionService),
    upload_service: RLUploadService = ReqDep(RLUploadService),
):
    restricted_list: Dict = service.get_restricted_list(restricted_list_id)
    try:
        buffer, errors = await upload_service.validate_input_file(file, restricted_list)
        if errors:
            response.status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
            return RestrictedListUploadResponse.parse_obj({"rowErrors": errors})
        else:
            file_path = Path(file.filename)
            restriction_service.delete_restricted_list_restrictions(restricted_list_id)
            uploaded_key = upload_service.upload_list(
                buffer, Path(f"{str(restricted_list_id)}{file_path.suffix}")
            )
            await service.update_restricted_list(
                restricted_list_id=restricted_list_id,
                restricted_list_in=OptionalRestrictedListIn(**restricted_list),
            )
    except ValueError as e:
        response.status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        return RestrictedListUploadResponse.parse_obj({"fileError": str(e)})

    return RestrictedListUploadResponse.parse_obj({"uploadedKey": uploaded_key})


@router.get(
    path="/{restricted_list_id}/ingest-file/file-audit",
    name="restricted-list:ingest-file-audit",
)
async def get_ingested_sfa(
    restricted_list_id: UUID,
    repo: SinkFileAuditRepository = ReqDep(SinkFileAuditRepository),
):
    """Retrieve the status of files uploaded for ingestion using sink file
    audits."""

    raw_result = await repo.get_restricted_list_upload_file_audits(restricted_list_id)
    return SearchResult.from_raw_result(raw_result)


@router.get(
    path="/{restricted_list_id}",
    response_model=RestrictedListOut,
    response_model_by_alias=False,
)
async def get_restricted_list(
    request: Request,
    restricted_list_id: UUID,
    service: RestrictedListService = ReqDep(RestrictedListService),
    mb: FastApiMessageBus = Depends(),
):
    """Returns a restricted list, including attachments of the list."""
    restricted_list = service.get_restricted_list(restricted_list_id)

    await mb.publish(events.RestrictedListViewed(record_obj=restricted_list, request=request))
    return restricted_list


@router.get(
    path="/restriction/summary",
    name="restricted-list:get-restriction-summary",
    response_model_by_alias=False,
)
async def get_restricted_list_restriction_summary(
    request: Request,
    restricted_list_id: Optional[UUID] = Query(None, alias="restrictedListId"),
    with_active_watches: Optional[bool] = Query(
        True, alias="live", description="Includes only lists with active watches when set to True"
    ),
    params: PaginatedDatedListParams = Depends(),
    service: RestrictedListService = ReqDep(RestrictedListService),
    mb: FastApiMessageBus = Depends(),
):
    """Returns a summary of restrictions of the restricted list(s).

    When `live` query param is passed:
    - It returns counts of instruments and issuers.

    When `restricted_list_id` is passed:
    - It returns counts of instruments, issuers, parties and validationErrors.
    """

    response = await service.get_restriction_summary(
        with_active_watches=with_active_watches,
        **({"restricted_list_id": str(restricted_list_id)} if restricted_list_id else {}),
        **params.as_search_kwargs(),
    )

    await mb.publish(events.RestrictedListViewed(record_obj=response, request=request))
    return response


@router.put(
    path="/{restricted_list_id}",
    response_model=RestrictedListOut,
    response_model_by_alias=False,
)
async def update_restricted_list(
    request: Request,
    restricted_list_id: UUID,
    data: OptionalRestrictedListIn = Depends(PendingBody(OptionalRestrictedListIn)),
    mb: FastApiMessageBus = Depends(),
    service: RestrictedListService = ReqDep(RestrictedListService),
):
    if data.id is not None and data.id != restricted_list_id:
        raise EditConflict(message="Id in the request is different from the object to be updated")

    changes, restricted_list = await service.update_restricted_list(
        restricted_list_id=restricted_list_id, restricted_list_in=data
    )

    await mb.publish(
        events.RestrictedListUpdated(record_obj=restricted_list, changes=changes, request=request)
    )
    return restricted_list


@router.delete(
    path="/{restricted_list_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_restricted_list(
    request: Request,
    restricted_list_id: UUID,
    mb: FastApiMessageBus = Depends(),
    service: RestrictedListService = ReqDep(RestrictedListService),
):
    restricted_list = await service.delete_restricted_list(restricted_list_id)

    await mb.publish(events.RestrictedListDeleted(record_obj=restricted_list, request=request))
    return None


@router.post(
    path="/{restricted_list_id}/attachments",
    name="restricted-list:add-restricted-list-attachments",
    response_model=list[AttachmentOut],
    response_model_by_alias=False,
    status_code=status.HTTP_201_CREATED,
)
def create_restricted_list_attachment(
    restricted_list_id: UUID,
    data: list[AttachmentIn],
    service: RestrictedListService = ReqDep(RestrictedListService),
):
    return service.add_attachment_to_restricted_list(
        restricted_list_id=restricted_list_id,
        attachments=data,
    )


@router.get(
    path="/{restricted_list_id}/watches",
    name="restricted-list:get-list-watches",
)
async def get_restricted_list_watches(
    request: Request,
    restricted_list_id: UUID,
    status: WatchStatusType = Query(default=None),
    service: RestrictedListService = ReqDep(RestrictedListService),
    mb: FastApiMessageBus = Depends(),
):
    response = await service.get_restricted_list_watches(
        restricted_list_id=str(restricted_list_id), status=status
    )

    if response.results:
        await mb.publish(
            events.RestrictedListWatchesSearched(
                record_obj=response.results,
                restricted_list_id=str(restricted_list_id),
                request=request,
            )
        )
    return response


@router.get(
    path="/{restricted_list_id}/errors/summary",
    name="restricted-list:validationErrors-summary",
)
def get_restricted_list_validation_errors_summary(
    restricted_list_id: UUID,
    params: NonPaginatedDatedListParams = Depends(),
    service: RestrictedListService = ReqDep(RestrictedListService),
):
    response = service.get_validation_errors_summary(
        restricted_list_id=str(restricted_list_id), **params.as_search_kwargs()
    )
    return response


@router.post(
    path="/upload-template",
    name="restricted-list:upload-template",
)
def get_template(file_extension: FileExtension = Query(default="csv", alias="fileExtension")):
    """Returns an upload file template in the specified format (csv or
    xlsx)."""
    if file_extension == "xlsx":
        return service.get_xlsx_upload_template()

    return service.get_csv_upload_template()
