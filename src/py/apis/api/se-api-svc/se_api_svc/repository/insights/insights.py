from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.repository.elastic import EsRepository
from api_sdk.security.policy.engine import PolicyResult
from api_sdk.utils.policy import apply_policy_to_raw_query
from se_api_svc.api.routes.insights.common import InsightsModuleReportTypes
from se_api_svc.repository.insights.queries import SEARCH_MODEL_BY_REPORT_TYPE
from se_api_svc.schemas.insights import InsightsReport
from typing import Dict, Optional


class InsightsRepositoryAdapter:
    """insights-query-utils expects a certain interface which we don't need to
    be bound by in our "repository" classes so instead of passing the repos,
    pass this adapter."""

    def __init__(self, index, repo: EsRepository, policy: Optional[PolicyResult]):
        self._index = index
        self._repo = repo
        self._policy = policy

    async def scroll(self, query: Dict, verbose: bool = True):
        """This loads everything in memory before returning, but that's how
        insights-query-utils expects this."""
        policied_query = apply_policy_to_raw_query(policy=self._policy, query=query)
        return list(await self._repo.scan(query=policied_query, index=self._index))

    async def search(self, query: Dict, index: str = None):
        policied_query = apply_policy_to_raw_query(policy=self._policy, query=query)
        # TODO increasing request_timeout for debug
        return await self._repo.search(
            query=policied_query, index=index or self._index, request_timeout=600
        )

    async def scroll_composite_aggs(self, query: Dict, index: str = None):
        policied_query = apply_policy_to_raw_query(policy=self._policy, query=query)
        res = []
        after_key = None
        while True:
            if len(res) >= 10_000:
                break

            policied_query["aggs"]["composite_buckets"]["composite"]["size"] = 5000

            if after_key:
                policied_query["aggs"]["composite_buckets"]["composite"]["after"] = after_key

            if "track_total_hits" in policied_query:
                policied_query.pop("track_total_hits")

            tmp_res = await self._repo.search(
                query=policied_query, index=index or self._index, request_timeout=600
            )
            records = tmp_res.get("aggregations").get("composite_buckets").get("buckets")
            after_key = tmp_res.get("aggregations").get("composite_buckets").get("after_key")

            if len(records) > 0:
                res.extend(records)
            else:
                break
        return res


class InsightsRepository(RepoHelpersMixin):
    async def get_reports(self, report_type: InsightsModuleReportTypes, **kwargs):
        return await self.repo.get_many(
            record_model=InsightsReport,
            search_model_cls=SEARCH_MODEL_BY_REPORT_TYPE[report_type],
            default_sort=["&timestamp:desc"],
            **kwargs,
        )
