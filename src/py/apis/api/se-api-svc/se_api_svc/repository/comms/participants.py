# type: ignore
import datetime as dt
from api_sdk.es_dsl.base import (
    And,
    Dsl<PERSON><PERSON>y<PERSON>ilter,
    ModelFilter,
    Not,
    NotExpiredFilter,
    Or,
    SearchFeature,
    SearchFeatureConfig,
    SearchModel,
    TermFilter,
)
from api_sdk.es_dsl.features import Nested
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import ModelParams
from api_sdk.es_dsl.utils import sanitize_es_reserved_characters
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from calendar import day_name
from dataclasses import dataclass
from elasticsearch_dsl import Q
from se_api_svc.repository.comms.scripts.participants import (
    CATEGORIZATION_AGG_SCRIPT,
    CATEGORIZATION_DETAILED_FILTER_SCRIPT,
    CATEGORIZATIONS_AGG_MAP_SCRIPTS,
)
from se_api_svc.repository.comms.utils.participants import (
    DEPTH_1_NODE_COUNT,
    DEPTH_2_NODE_COUNT,
    EXCLUDE_PARTICIPANTS_AGG,
    LINKS_AGG,
    NODE_AGG,
    PARTICIPANT_AGG,
    REVERSE_AGG,
    NetworkNode,
    get_initials_from_name,
    get_network_map_nodes_and_links,
)
from se_api_svc.schemas.account import AccountPerson
from se_api_svc.schemas.comms import Call, Email, Message, Text
from se_api_svc.schemas.comms.common import (
    GetParticipantHeatmapIn,
    ParticipantCategorizations,
    ParticipantCategorizationsDetailed,
    ParticipantCategorizationTrends,
    ParticipantsDirections,
    ParticipantTrends,
)
from se_api_svc.schemas.comms.meeting import Meeting
from se_api_svc.schemas.market import MarketPerson
from se_elastic_schema.static.communication import (
    ParticipationType as CommunicationParticipationType,
)
from se_elastic_schema.static.reference import (
    PersonStructureInstrumentAssetClass,
    PersonStructureSmcrFunction,
    PersonStructureSmcrFunctionCategory,
    PersonStructureType,
)
from se_elastic_schema.static.surveillance import ParticipantQueryType, ParticipantType
from typing import List, Optional, Union

DIRECTIONS_MAP = {
    ParticipantsDirections.RECIPIENT: [
        CommunicationParticipationType.TO,
        CommunicationParticipationType.CC,
        CommunicationParticipationType.BCC,
    ],
    ParticipantsDirections.SENDER: [
        CommunicationParticipationType.FROM,
        CommunicationParticipationType.BEHALF,
    ],
}

PARTICIPANT_QUERY_TYPES_MAP = {
    ParticipantQueryType.BETWEEN: list(CommunicationParticipationType),
    ParticipantQueryType.INVOLVING: list(CommunicationParticipationType),
    ParticipantQueryType.RECEIVED_BY: [
        CommunicationParticipationType.TO,
        CommunicationParticipationType.CC,
        CommunicationParticipationType.BCC,
    ],
    ParticipantQueryType.SENT_BY: [
        CommunicationParticipationType.FROM,
        CommunicationParticipationType.BEHALF,
    ],
}

PARTICIPANTS_FIELDS_MAP = {
    ParticipantTrends.NAME: {
        ParticipantTrends.NAME.value: ("participants.value.name", "participants", False)
    },
    ParticipantTrends.DOMAIN: {
        ParticipantTrends.DOMAIN.value: ("identifiers.domains.value", "identifiers.domains", False)
    },
    ParticipantTrends.ID: {
        "FROM_ID": ("identifiers.fromId", False, False),
        "BEHALF_ID": ("identifiers.onBehalfOf", False, False),
        "TO_ID": ("identifiers.toIds", False, False),
        "CC_ID": ("identifiers.ccIds", False, False),
        "BCC_ID": ("identifiers.bccIds", False, False),
    },
    ParticipantTrends.NATIONALITY: {
        ParticipantTrends.NATIONALITY.value: (
            "participants.value.personalDetails.nationality",
            "participants",
            False,
        )
    },
    ParticipantTrends.HOME_ADDRESS_COUNTRY: {
        ParticipantTrends.HOME_ADDRESS_COUNTRY.value: (
            "participants.value.location.homeAddress.country",
            "participants",
            False,
        )
    },
    ParticipantTrends.HOME_ADDRESS_CITY: {
        ParticipantTrends.HOME_ADDRESS_CITY.value: (
            "participants.value.location.homeAddress.city",
            "participants",
            False,
        )
    },
    ParticipantTrends.OFFICE_ADDRESS_COUNTRY: {
        ParticipantTrends.OFFICE_ADDRESS_COUNTRY.value: (
            "participants.value.location.officeAddress.country",
            "participants",
            False,
        )
    },
    ParticipantTrends.OFFICE_ADDRESS_CITY: {
        ParticipantTrends.OFFICE_ADDRESS_CITY.value: (
            "participants.value.location.officeAddress.city",
            "participants",
            False,
        )
    },
    ParticipantTrends.CLIENT_MANDATE: {
        ParticipantTrends.CLIENT_MANDATE.value: (
            "participants.value.officialIdentifiers.clientMandate",
            "participants",
            False,
        )
    },
    ParticipantTrends.EMPLOYEE_ID: {
        ParticipantTrends.EMPLOYEE_ID.value: (
            "participants.value.officialIdentifiers.employeeId",
            "participants",
            False,
        )
    },
    ParticipantTrends.TRADER_ID: {
        ParticipantTrends.TRADER_ID.value: (
            "participants.value.officialIdentifiers.traderIds.label",
            "participants",
            False,
        )
    },
    ParticipantTrends.DECISION_MAKER: {
        ParticipantTrends.DECISION_MAKER.value: (
            "participants.value.structure.isDecisionMaker",
            "participants",
            False,
        )
    },
    ParticipantTrends.INSTRUMENT: {
        ParticipantTrends.INSTRUMENT.value: (
            "participants.value.structure.instruments.ableToTradeBestExAssetClass",
            "participants",
            PersonStructureInstrumentAssetClass,
        )
    },
    ParticipantTrends.ROLE: {
        ParticipantTrends.ROLE.value: ("participants.value.structure.role", "participants", False)
    },
    ParticipantTrends.SMCR_FUNCTION: {
        ParticipantTrends.SMCR_FUNCTION.value: (
            "participants.value.structure.smcr.functions.function",
            "participants",
            PersonStructureSmcrFunction,
        )
    },
    ParticipantTrends.SMCR_FUNCTION_CATEGORY: {
        ParticipantTrends.SMCR_FUNCTION_CATEGORY.value: (
            "participants.value.structure.smcr.functionCategory",
            "participants",
            PersonStructureSmcrFunctionCategory,
        )
    },
    ParticipantTrends.SMCR_REVIEW_DATE: {
        ParticipantTrends.SMCR_REVIEW_DATE.value: (
            "participants.value.structure.smcr.reviewDate",
            "participants",
            False,
        )
    },
    ParticipantTrends.DEPARTMENT: {
        ParticipantTrends.DEPARTMENT.value: (
            "participants.value.structure.department",
            "participants",
            False,
        )
    },
    ParticipantTrends.TYPE: {
        ParticipantTrends.TYPE.value: (
            "participants.value.structure.type",
            "participants",
            PersonStructureType,
        )
    },
    ParticipantTrends.DESK_ID: {
        ParticipantTrends.DESK_ID.value: (
            "participants.value.structure.desks.id",
            "participants.value.structure.desks",
            False,
        )
    },
    ParticipantTrends.DESK: {
        ParticipantTrends.DESK.value: (
            "participants.value.structure.desks.name",
            "participants.value.structure.desks",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_COUNTRY: {
        ParticipantTrends.JURISDICTION_COUNTRY.value: (
            "participants.value.monitoring.jurisdictionCountry",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_REGION: {
        ParticipantTrends.JURISDICTION_REGION.value: (
            "participants.value.monitoring.jurisdictionRegion",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_LEGAL_ENTITY: {
        ParticipantTrends.JURISDICTION_LEGAL_ENTITY.value: (
            "participants.value.monitoring.jurisdictionLegalEntity",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_CUSTOM1: {
        ParticipantTrends.JURISDICTION_CUSTOM1.value: (
            "participants.value.monitoring.custom1",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_CUSTOM2: {
        ParticipantTrends.JURISDICTION_CUSTOM2.value: (
            "participants.value.monitoring.custom2",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_CUSTOM3: {
        ParticipantTrends.JURISDICTION_CUSTOM3.value: (
            "participants.value.monitoring.custom3",
            "participants",
            False,
        )
    },
    ParticipantTrends.JURISDICTION_BUSINESS_LINE: {
        ParticipantTrends.JURISDICTION_BUSINESS_LINE.value: (
            "participants.value.monitoring.jurisdictionBusinessLine",
            "participants",
            False,
        )
    },
    ParticipantTrends.IS_MONITORED: {
        ParticipantTrends.IS_MONITORED.value: (
            "participants.value.monitoring.isMonitored",
            "participants",
            False,
        )
    },
    ParticipantTrends.IS_MONITORED_FOR: {
        ParticipantTrends.IS_MONITORED_FOR.value: (
            "participants.value.monitoring.isMonitoredFor",
            "participants",
            False,
        )
    },
    ParticipantTrends.MONITORING_RISK_LEVEL: {
        ParticipantTrends.MONITORING_RISK_LEVEL.value: (
            "participants.value.monitoring.risk",
            "participants",
            False,
        )
    },
    ParticipantTrends.BRANCH_COUNTRY: {
        ParticipantTrends.BRANCH_COUNTRY.value: (
            "participants.value.officialIdentifiers.branchCountry",
            "participants",
            False,
        )
    },
    ParticipantTrends.LOCAL_PARTS: {
        ParticipantTrends.LOCAL_PARTS.value: (
            "identifiers.localParts.value",
            "identifiers.localParts",
            False,
        )
    },
}


class CategorizationsFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "categorizations"
        categorizations: Optional[List[ParticipantCategorizations]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        def get_prefix_filter(prefix_key: ParticipantType):
            return Nested(
                DslQueryFilter(Q("prefix", **{"participants.value.&key": prefix_key})),
                path="participants",
                ignore_unmapped=True,
            )

        categorizations = getattr(params, self.config.param, None) or self.config.categorizations
        if not categorizations:
            return

        filters = []

        for categorization in categorizations:
            if categorization == ParticipantCategorizations.INTERNAL_AND_EXTERNAL:
                filters.append(
                    And(
                        get_prefix_filter(ParticipantType.ACCOUNTPERSON),
                        get_prefix_filter(ParticipantType.MARKETPERSON),
                    )
                )

            elif categorization == ParticipantCategorizations.INTERNAL_ONLY:
                filters.append(Not(get_prefix_filter(ParticipantType.MARKETPERSON)))

            elif categorization == ParticipantCategorizations.EXTERNAL_ONLY:
                filters.append(Not(get_prefix_filter(ParticipantType.ACCOUNTPERSON)))

        return Or(*filters).build_q(meta_fields=meta_fields)


class DirectionsFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "directions"
        directions: Optional[List[ParticipantsDirections]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        directions: List[ParticipantsDirections] = (
            getattr(params, self.config.param, None) or self.config.directions
        )
        if not directions:
            return

        filters = []
        for direction in directions:
            filters.append(
                Nested(
                    TermFilter(name="participants.types", value=DIRECTIONS_MAP[direction]),
                    path="participants",
                )
            )

        return Or(*filters).build_q(meta_fields=meta_fields)


class HeatmapFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "heatmap"
        heatmaps: Optional[List[GetParticipantHeatmapIn]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        heatmaps = getattr(params, self.config.param, None) or self.config.heatmaps
        if not heatmaps:
            return

        filters = []
        for heatmap in heatmaps:
            if isinstance(heatmap.day, int) and isinstance(heatmap.hour, int):
                script = {
                    "script": {
                        "lang": "painless",
                        "params": {"timezone": params.timezone},
                        "inline": (
                            f"doc['timestamps.timestampStart'].size() > 0 && "
                            f"ZonedDateTime.ofInstant(doc['timestamps.timestampStart'][0].toInstant(),"
                            f"ZoneId.of('{params.timezone}')).getDayOfWeek().getValue() "
                            f"== {heatmap.day} && "
                            f"ZonedDateTime.ofInstant(doc['timestamps.timestampStart'][0].toInstant(),"
                            f"ZoneId.of('{params.timezone}')).getHour() == {heatmap.hour}"
                        ),
                    }
                }
                filters.append(DslQueryFilter(Q("script", **script)))

        return Or(*filters).build_q(meta_fields=meta_fields)


class ParticipantsFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "participant_ids"
        participant_ids: Optional[List[str]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        participant_ids = getattr(params, self.config.param, None) or self.config.participant_ids
        if not participant_ids:
            return

        return Nested(
            TermFilter(name="participants.value.&id", value=participant_ids),
            path="participants",
        ).build_q(meta_fields=meta_fields)


class CategorizationsDetailedFilter(SearchFeature):
    @dataclass
    class Config(SearchFeatureConfig):
        param: Optional[str] = "categorizations_detailed"
        categorizations_detailed: Optional[List[ParticipantCategorizationsDetailed]] = None

    config: Config

    def build_q(self, params=None, *, meta_fields):
        categorizations_detailed = (
            getattr(params, self.config.param, None) or self.config.categorizations_detailed
        )
        if not categorizations_detailed:
            return

        script = {
            "script": {
                "lang": "painless",
                "params": {
                    "ids": categorizations_detailed,
                },
                "inline": CATEGORIZATION_DETAILED_FILTER_SCRIPT,
            }
        }

        return Q("script", **script)


class ParticipantsSearchBase(SearchModel):
    class Params(ModelParams):
        participant_id: Optional[str] = None
        categorizations: Optional[List[ParticipantCategorizations]] = None
        categorizations_detailed: Optional[List[ParticipantCategorizationsDetailed]] = None
        directions: Optional[List[ParticipantsDirections]] = None
        end: Union[dt.datetime, dt.date] = None
        timezone: Optional[str] = None
        f: Optional[str] = None
        heatmaps: Optional[List[GetParticipantHeatmapIn]] = None
        participant_ids: Optional[List[str]] = None
        start: Union[dt.datetime, dt.date] = None

    params: Params

    features = [
        # Keep the ModelFilter, NotExpiredFilter and FlangFilter on Top.
        ModelFilter(model=[Call, Email, Meeting, Message, Text]),
        NotExpiredFilter,
        FlangFilter.simple(param="f", nested_paths=["participants"]),
        CategorizationsDetailedFilter(param="categorizations_detailed"),
        CategorizationsFilter(param="categorizations"),
        DirectionsFilter(param="directions"),
        HeatmapFilter(param="heatmaps"),
        Nested(
            TermFilter(name="participants.value.&id", param="participant_id"),
            path="participants",
        ),
        ParticipantsFilter(param="participant_ids"),
    ]


class ParticipantsSummaryByHourByWeekdayAggs(ParticipantsSearchBase):
    class Params(ParticipantsSearchBase.Params):
        timezone: Optional[str]

    params: Params

    def build_aggs(self):
        """Builds aggregations Participant Comms by hour, by weekday and then
        by data sources.

        1. HOUR_OF_DAY: histogram aggregation by hour of day
        1.1. DAY_OF_WEEK: histogram aggregation histogram by day of week
        1.1.1. DATA_SOURCE: term aggregation by client data source
        """
        return {
            "HOUR_OF_DAY": {
                "histogram": {
                    "script": {
                        "source": f"def ts = doc['timestamps.timestampStart'].value."
                        f"toInstant().toEpochMilli(); def localTs "
                        f"= LocalDateTime.ofInstant(Instant.ofEpochMilli(ts),"
                        f" ZoneId.of('{self.params.timezone}'));"
                        f" return localTs.getHour();"
                    },
                    "min_doc_count": 1,
                    "interval": "1",
                    "extended_bounds": {"min": 0, "max": 23},
                },
                "aggs": {
                    "DAY_OF_WEEK": {
                        "histogram": {
                            "script": {
                                "source": f"def ts = doc['timestamps.timestampStart'].value."
                                f"toInstant().toEpochMilli(); def localTs = LocalDateTime."
                                f"ofInstant(Instant.ofEpochMilli(ts), ZoneId."
                                f"of('{self.params.timezone}')); return localTs.getDayOfWeek()."
                                f"getValue();"
                                # noqa
                            },
                            "min_doc_count": 1,
                            "interval": "1",
                        },
                        "aggs": {
                            "DATA_SOURCE": {
                                "terms": {
                                    "field": "metadata.source.client",
                                    "size": 100,
                                    "min_doc_count": 1,
                                }
                            }
                        },
                    }
                },
            }
        }


class ParticipantsSummaryByDirectionAggs(ParticipantsSearchBase):
    def build_aggs(self):
        """Builds Direction Aggregation for RECIPIENT and SENDER comm
        direction."""

        # Build directions agg for recipient and sender
        def build_direction_agg(direction: ParticipantsDirections, participant_id: str):
            """Build Nested Aggregation on Participants based on the given
            direction type.

            The Agg is filtered participant_id and by direction of comm.
            """
            return {
                "nested": {"path": "participants"},
                "aggs": {
                    "aggs": {
                        "filter": {
                            "bool": {
                                "must": [
                                    {"terms": {"participants.value.&id": [participant_id]}},
                                    {"terms": {"participants.types": DIRECTIONS_MAP[direction]}},
                                ]
                            }
                        },
                        "aggs": {"reverse_comms": {"reverse_nested": {}}},
                    }
                },
            }

        return {
            ParticipantsDirections.RECIPIENT.value: build_direction_agg(
                direction=ParticipantsDirections.RECIPIENT,
                participant_id=self.params.participant_id,
            ),
            ParticipantsDirections.SENDER.value: build_direction_agg(
                direction=ParticipantsDirections.SENDER, participant_id=self.params.participant_id
            ),
        }


class ParticipantsSummaryByCategorizationsAggs(ParticipantsSearchBase):
    class Params(ParticipantsSearchBase.Params):
        trend: ParticipantCategorizationTrends

    params: Params

    def build_aggs(self):
        """Builds Scripted Aggregation for the given categorization trends."""
        return {
            "NATURE_OF_PARTICIPANTS": {
                "scripted_metric": {
                    **CATEGORIZATION_AGG_SCRIPT,
                    "map_script": CATEGORIZATIONS_AGG_MAP_SCRIPTS[self.params.trend],
                }
            }
        }


class ParticipantsNetworkMapAggs(ParticipantsSearchBase):
    class Params(ParticipantsSearchBase.Params):
        depth_node_count: int
        exclude_participant_ids: Optional[List[str]] = []

    params: Params

    def build_aggs(self):
        def build_nested_participant_agg(include_ids: List[str], exclude_ids: List[str]):
            """Builds Scripted Aggregation for the given categorization trends.

            1. Create nested ONLY_ID Agg
            1.1. REVERSE_AGG: Term Filter on the included ids
            1.1.1. PARTICIPANT_AGG: Nested Participant Agg
            *******. EXCLUDE_PARTICIPANTS_AGG: We need to exclude participant ids so that we don't
             get nodes for them
            *******.1. LINKS_AGG: Aggreagate on link node using the given script that basically
             calculates the number
                                  communications for the given participant.
            """
            return {
                "nested": {"path": "participants"},
                "aggs": {
                    NODE_AGG: {
                        "filter": {"filter": {"terms": {"participants.value.&id": include_ids}}},
                        "aggs": {
                            REVERSE_AGG: {
                                "aggs": {
                                    PARTICIPANT_AGG: {
                                        "nested": {"path": "participants"},
                                        "aggs": {
                                            EXCLUDE_PARTICIPANTS_AGG: {
                                                "filter": {
                                                    "bool": {
                                                        "must_not": [
                                                            {
                                                                "terms": {
                                                                    "participants.value.&id": include_ids  # noqa
                                                                    + exclude_ids
                                                                }
                                                            }
                                                        ]
                                                    }
                                                },
                                                "aggs": {
                                                    LINKS_AGG: {
                                                        "terms": {
                                                            "size": self.params.depth_node_count,
                                                            "script": {
                                                                "inline": "doc['participants.value.&id']."  # noqa
                                                                "value + '|' "
                                                                "+ doc['participants.value.name']."
                                                                "value + '|' "
                                                                "+ doc['participants.value.&key']."
                                                                "value.substring(0,3)"
                                                            },
                                                        }
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                                "reverse_nested": {},
                            }
                        },
                    }
                },
            }

        """
        For each included participant create an agg to find the nodes and links associated
        with the same.
        """
        aggs = {}
        for participant_id in self.params.participant_ids:
            aggs[participant_id] = build_nested_participant_agg(
                include_ids=[participant_id], exclude_ids=self.params.exclude_participant_ids
            )

        return aggs


class ParticipantsSummaryByTrendAggs(ParticipantsSearchBase):
    """Class filter to build aggregate query for participant trends."""

    class Params(ParticipantsSearchBase.Params):
        participant_type: ParticipantType
        query_type: ParticipantQueryType
        trend: ParticipantTrends
        trend_search: Optional[str]

    params: Params

    def get_participants_fields(self):
        fields = PARTICIPANTS_FIELDS_MAP[self.params.trend]

        if self.params.trend == ParticipantTrends.ID:
            participant_types = [
                f"{p.value}_ID"
                for p in PARTICIPANT_QUERY_TYPES_MAP[self.params.query_type]
                if f"{p.value}_ID" in fields.keys()
            ]
            fields = {k: fields[k] for k in participant_types}

        return fields.items()

    def build_aggs(self):
        """
        Generates Aggregate query for participants by Trend + Participant Type + Query Type
        For each PARTICIPANTS_FIELDS_MAP by trend, generate the following aggregation:
        1. Get the mapping details. i.e. field and nested_field
        2. if the field is nested, add a nested query. Add respective filters if nested field is
         `participants` or
           `identifiers.domains`. For all other case either add filters to it or use match_all
            filter
        3. else use the term query for the aggregation.

        Eg:
        Non nested multiple field aggregations
        {
            "FROM_ID": {"terms": {"field": "identifiers.fromId", "size": 2147483647}},
            "BEHALF_OF": {"terms": {"field": "identifiers.onBehalfOf", "size": 2147483647}},
            "TO_ID": {"terms": {"field": "identifiers.toIds", "size": 2147483647}},
            "CC_ID": {"terms": {"field": "identifiers.ccIds", "size": 2147483647}},
            "BCC_ID": {"terms": {"field": "identifiers.bccIds","size": 2147483647}}
        }

        Nested field aggregations
        {
            "TYPE": {
                "nested": {"path": "participants"},
                "aggs": {
                    "FILTER": {
                        "filter": {
                            "bool": {"must": [{"terms": {"participants.types":
                             ["FROM","TO","CC","BCC","BEHALF"]}}]}
                        },
                        "aggs": {"TREND": {"terms": {"field":
                        "participants.value.structure.type","size": 2147483647}}}
                    }
                }
            }
        }
        """

        agg_query = {}

        for key, values in self.get_participants_fields():
            field, nested_field, default_values = values
            trend_query = {"terms": {"field": field, "size": 10}}

            if nested_field:
                trend_query["terms"].update({"aggs": {"COMMS_COUNT": {"reverse_nested": {}}}})
                filter_query = {
                    "FILTER": {"aggs": {"TREND": trend_query}, "filter": {"match_all": {}}}
                }
                additional_filters = []

                if nested_field in [
                    "participants",
                    "identifiers.domains",
                    "identifiers.localParts",
                ]:
                    additional_filters.append(
                        {
                            "terms": {
                                f"{nested_field}.types": PARTICIPANT_QUERY_TYPES_MAP[
                                    self.params.query_type
                                ]
                            }
                        }
                    )

                if (
                    nested_field == "participants"
                    and self.params.participant_type != ParticipantType.BOTH
                ):
                    additional_filters.append(
                        {"prefix": {"participants.value.&key": self.params.participant_type.value}}
                    )

                if self.params.trend_search:
                    additional_filters.append(
                        {
                            "query_string": {
                                "query": f"*{sanitize_es_reserved_characters(self.params.trend_search)}*",  # noqa
                                "default_operator": "AND",
                                "analyze_wildcard": "true",
                                "allow_leading_wildcard": "true",
                                "default_field": f"{field}.*",
                            }
                        }
                    )

                if additional_filters:
                    filter_query["FILTER"]["filter"] = {"bool": {"filter": additional_filters}}

                agg_query[key] = {"nested": {"path": nested_field}, "aggs": filter_query}
            else:
                agg_filter = (
                    {
                        "query_string": {
                            "query": f"*{sanitize_es_reserved_characters(self.params.trend_search)}*",  # noqa
                            "default_operator": "AND",
                            "analyze_wildcard": "true",
                            "allow_leading_wildcard": "true",
                            "default_field": f"{field}.*",
                        }
                    }
                    if self.params.trend_search
                    else {"match_all": {}}
                )
                if self.params.trend_search is not None:
                    trend_query["terms"]["include"] = (
                        f".*{self.params.trend_search}.*|.*{self.params.trend_search.lower()}.*|.*{self.params.trend_search.upper()}.*"  # noqa: E501
                    )
                agg_query[key] = {"filter": agg_filter, "aggs": {"COUNT": trend_query}}

        return agg_query


class ParticipantsRepository(RepoHelpersMixin):
    async def get_summary_by_hour_by_weekday(self, **search_params):
        """Get Summary for Participant Communications by hour, by weekday and
        then by data sources.

        Returns:
        --------
        [{
            "key": string,  // hour of day (0 -> 23)
            "count": int,
            "buckets": [{
                "key": string,  // week of day (1 -> 7)
                "title": string, // week day name
                "count: int,
                "buckets": [{
                    "key": string,  // data source
                    "count": int
                }]
            }]
        }]
        """
        agg_result = await self.get_aggs(
            search_model_cls=ParticipantsSummaryByHourByWeekdayAggs, **search_params
        )

        result = []
        # Fill the empty day of hour and week days if no agg data is present for the same.
        for hour_index in range(0, 24):
            hour_agg = next(
                (
                    b
                    for b in agg_result.aggregations.HOUR_OF_DAY.buckets
                    if int(b.key) == hour_index
                ),
                None,
            )
            hour = {
                "key": hour_index,
                "count": hour_agg.doc_count if hour_agg else 0,
                "buckets": [],
            }

            for day_of_week_index in range(1, 8):
                day_of_week_agg = next(
                    (
                        b
                        for b in (hour_agg.DAY_OF_WEEK.buckets if hour_agg else [])
                        if int(b.key) == day_of_week_index
                    ),
                    None,
                )
                day_of_week = {
                    "key": day_of_week_index,
                    "title": day_name[int(day_of_week_index - 1)],
                    "count": day_of_week_agg.doc_count if day_of_week_agg else 0,
                    "buckets": [],
                }
                if day_of_week_agg:
                    for data_source_agg in day_of_week_agg.DATA_SOURCE.buckets:
                        day_of_week["buckets"].append(
                            {"key": data_source_agg.key, "count": data_source_agg.doc_count}
                        )

                hour["buckets"].append(day_of_week)

            result.append(hour)

        return result

    async def get_trends_summary_by_directions(self, **search_params):
        """Get Direction Summary for comms participants.

        Returns:
        --------
        [{"key": string, "count": int}]

        Eg:
        [
            {"key": "RECIPIENT", "count": 123},
            {"key": "SENDER", "count": 123},
        ]
        """
        agg_result = (
            await self.get_aggs(
                search_model_cls=ParticipantsSummaryByDirectionAggs, **search_params
            )
        ).aggregations

        return [
            {"key": direction, "count": agg_result[direction].aggs.doc_count}
            for direction in ParticipantsDirections
        ]

    async def get_trends_summary_by_categorizations(self, **search_params):
        """Get Summary for comms participants by categorizations and
        categorizations detailed.

        Returns:
        --------
        [{"key": string, "count": int}]

        Eg:
        [
            {"key": "RECIPIENT", "count": 123},
            {"key": "SENDER", "count": 123},
        ]
        """
        aggs = await self.get_aggs(
            search_model_cls=ParticipantsSummaryByCategorizationsAggs, **search_params
        )
        values_map = aggs.aggregations.NATURE_OF_PARTICIPANTS.value
        return [
            {"key": categorization, "count": values_map[categorization]}
            for categorization in values_map.keys()
        ]

    async def get_network_map(self, participant_id: str, **search_params):
        """Get Summary for comms participants by categorizations and
        categorizations detailed.

        Steps:
        1. Find the Participant record to get participant's name and model
        2. Create a root node as the primary participant.
        3. Using the aggregate response generate first depth (from parent participant's view)
         nodes and links
        4. If there are nodes in first depth, use the node and link map to get the depth 2 link.
         Please note that
           now we now need to search apart from the parent node and look for all the nodes
           found from depth 1.
        5. Calculate the max links and then find update the normalised weight for all the links.
        6. Return the nodes and links.

        Returns:
        --------
        {"nodes": List[NetworkNode], "link": List[NetworkLink]}
        """

        # Find participant and create root node
        primary_participant = await self.get_one(
            record_model=[AccountPerson, MarketPerson], id=participant_id
        )
        root_node = NetworkNode(
            id=primary_participant.id_,
            name=primary_participant.name,
            initials=get_initials_from_name(primary_participant.name),
            type=primary_participant.model_,
        )

        nodes_by_id = {}
        links_by_id = {}
        data_nodes = []
        data_links = []

        data_nodes.append(root_node)
        nodes_by_id[root_node.id] = root_node

        # Delete participant_ids param if present as we'll use this param to
        # search for included participants in the
        # query
        search_params.pop("participant_ids", None)

        # Get the nodes for the primary participant. We'll find a max of
        # `DEPTH_1_NODE_COUNT` nodes.
        depth_1_result = await self.get_aggs(
            search_model_cls=ParticipantsNetworkMapAggs,
            depth_node_count=DEPTH_1_NODE_COUNT,
            participant_ids=[root_node.id],
            **search_params,
        )
        depth_1_nodes, depth_1_links = get_network_map_nodes_and_links(
            depth_1_result, [root_node], nodes_by_id, links_by_id
        )
        data_nodes += depth_1_nodes
        data_links += depth_1_links

        # If nodes are present, now for these node as root node, find the nodes and
        # links for each included node
        if data_nodes:
            depth_2_result = await self.get_aggs(
                search_model_cls=ParticipantsNetworkMapAggs,
                depth_node_count=DEPTH_2_NODE_COUNT,
                participant_ids=[node.id for node in depth_1_nodes],
                exclude_participant_ids=[root_node.id],
                **search_params,
            )
            depth_2_nodes, depth_2_links = get_network_map_nodes_and_links(
                depth_2_result, depth_1_nodes, nodes_by_id, links_by_id
            )
            data_nodes += depth_2_nodes
            data_links += depth_2_links

        # Get the maximum comms link
        max_links_comms = max([link.comms for link in depth_1_links], default=0)

        # Calculate the new normalised weight.
        for link in data_links:
            link.normalised_weight = link.comms / max_links_comms

        return {"links": data_links, "nodes": data_nodes}

    async def get_trends_summary(self, trend: ParticipantTrends, **search_params):
        """Get the aggregated summary for participants by Trend + Query Type +
        Participant Type.

        Steps:
        1. Get aggregate results
        2. Create an empty results array in which all items will be appended.
        3. For each aggregate result
        3.1. Find the mapping details
        3.2. Get the bucket array
        3.3. If default enum is provided (it means that these items MUST always be present in the
         result) fill the
             correct counts, if available, into the correct items.
        3.4 Now we have the correct buckets with counts so let's iterate on the bucket.
        For eac item in bucket
        3.4.1. Find if the item has already been inserted. If yes, add the count to the
         it else insert the item.
        4. Sort the results by descending order of count and return the list.
        """

        # we do NOT want the search term to be added to the filters portion
        # of the base query. We only want the search to be used in the Aggs here
        trend_search = search_params.pop("model_qs", None)

        aggs_results = await self.get_aggs(
            search_model_cls=ParticipantsSummaryByTrendAggs,
            trend=trend,
            trend_search=trend_search,
            **search_params,
        )

        # Result array
        results = []

        # Note: For most case we only have single aggregation. The code skips the additional
        # logic and just generates
        # and returns the results for such cases.
        for agg_key in aggs_results.aggregations:
            field, nested_field, default_enum = PARTICIPANTS_FIELDS_MAP[trend][agg_key]

            buckets = list(
                aggs_results.iter_raw_bucket_agg(
                    f"{agg_key}.FILTER.TREND" if nested_field else f"{agg_key}.COUNT"
                )
            )

            # This is used to provide an array for the result. Fill the matched aggregations
            # bucket with the value of
            # these enums and add the count if present.
            if default_enum:
                default_enum_buckets = []
                for default_item in default_enum:
                    item_count = next(
                        (b["doc_count"] for b in buckets if b["key"] == default_item.value), 0
                    )
                    if item_count:
                        default_enum_buckets.append(
                            {"key": default_item.value, "doc_count": item_count}
                        )
                buckets = default_enum_buckets

            for item in buckets:
                # Finds the item index if it's already inserted in the array before.
                already_present_item_index = next(
                    (index for (index, a) in enumerate(results) if a["key"] == item["key"]), -1
                )

                doc_count = (
                    item["COMMS_COUNT"]["doc_count"]
                    if nested_field and "COMMS_COUNT" in item
                    else item["doc_count"]
                )

                if already_present_item_index == -1:
                    key = item["key"]
                    results.append(
                        {"key": key, "title": item.get("key_as_string", key), "count": doc_count}
                    )
                else:
                    # Increment the count if item already present in array.
                    results[already_present_item_index]["count"] += doc_count

        # Optimization: If there are multiple aggregations it means that the order of the results
        # might not be correct.
        # Hence, we need to reverse sort the results in these cases.
        return (
            sorted(results, key=lambda i: i["count"], reverse=True)
            if len(aggs_results.aggregations) > 0
            else results
        )
