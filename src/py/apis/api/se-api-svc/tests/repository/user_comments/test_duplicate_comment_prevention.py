# type: ignore
import datetime as dt
import pytest
from unittest.mock import AsyncMock, MagicMock
from se_api_svc.repository.user_comments.user_comments import UserCommentsRepository
from se_api_svc.schemas.user_comments import UserCommentIn
from se_elastic_schema.models.tenant.user.user_comment import UserComment


class TestDuplicateCommentPrevention:
    """Test cases for duplicate comment prevention in UserCommentsRepository."""

    @pytest.fixture
    def mock_repo(self):
        """Create a mock UserCommentsRepository for testing."""
        repo = MagicMock(spec=UserCommentsRepository)
        repo.search = AsyncMock()
        repo.save_new = AsyncMock()
        repo.index_for_record_model = MagicMock(return_value=["test_index"])
        repo.tenancy = MagicMock()
        repo.tenancy.userId = "test_user"
        repo.tenancy.user_name = "Test User"
        return repo

    @pytest.fixture
    def sample_comment_body(self):
        """Create a sample comment body for testing."""
        return UserCommentIn(
            comment="RESOLUTION: Resolved With Dismissal - Test resolution comment",
            linkId="test-alert-123",
            linkKey="Alert:test-alert-123:123456789",
            linkModel="OrderAlert",
            attachments=None,
            commentCategory=None
        )

    @pytest.mark.asyncio
    async def test_duplicate_comment_detected(self, mock_repo, sample_comment_body):
        """Test that duplicate comments are detected and prevented."""
        # Mock search result indicating duplicate exists
        mock_search_result = MagicMock()
        mock_search_result.hits.total = 1  # Indicates duplicate found
        mock_repo.search.return_value = mock_search_result

        # Create real instance with mocked methods
        repo = UserCommentsRepository.__new__(UserCommentsRepository)
        repo.search = mock_repo.search
        repo.save_new = mock_repo.save_new
        repo.index_for_record_model = mock_repo.index_for_record_model
        repo.tenancy = mock_repo.tenancy

        # Test the _is_duplicate_comment method
        is_duplicate = await repo._is_duplicate_comment(sample_comment_body)
        
        assert is_duplicate is True
        mock_repo.search.assert_called_once()

    @pytest.mark.asyncio
    async def test_no_duplicate_comment_found(self, mock_repo, sample_comment_body):
        """Test that non-duplicate comments are allowed."""
        # Mock search result indicating no duplicate exists
        mock_search_result = MagicMock()
        mock_search_result.hits.total = 0  # Indicates no duplicate found
        mock_repo.search.return_value = mock_search_result

        # Create real instance with mocked methods
        repo = UserCommentsRepository.__new__(UserCommentsRepository)
        repo.search = mock_repo.search
        repo.save_new = mock_repo.save_new
        repo.index_for_record_model = mock_repo.index_for_record_model
        repo.tenancy = mock_repo.tenancy

        # Test the _is_duplicate_comment method
        is_duplicate = await repo._is_duplicate_comment(sample_comment_body)
        
        assert is_duplicate is False
        mock_repo.search.assert_called_once()

    @pytest.mark.asyncio
    async def test_duplicate_comment_search_query_structure(self, mock_repo, sample_comment_body):
        """Test that the search query for duplicate detection is properly structured."""
        # Mock search result
        mock_search_result = MagicMock()
        mock_search_result.hits.total = 0
        mock_repo.search.return_value = mock_search_result

        # Create real instance with mocked methods
        repo = UserCommentsRepository.__new__(UserCommentsRepository)
        repo.search = mock_repo.search
        repo.index_for_record_model = mock_repo.index_for_record_model

        # Test the _is_duplicate_comment method
        await repo._is_duplicate_comment(sample_comment_body)
        
        # Verify the search was called with correct parameters
        mock_repo.search.assert_called_once()
        call_args = mock_repo.search.call_args
        
        # Check the search body structure
        search_body = call_args[1]["body"]
        assert "query" in search_body
        assert "bool" in search_body["query"]
        assert "must" in search_body["query"]["bool"]
        
        must_clauses = search_body["query"]["bool"]["must"]
        assert len(must_clauses) == 3  # linkId, comment, and timestamp range
        
        # Verify specific query clauses
        link_id_clause = next((clause for clause in must_clauses if "term" in clause and "linkId" in clause["term"]), None)
        assert link_id_clause is not None
        assert link_id_clause["term"]["linkId"] == sample_comment_body.linkId
        
        comment_clause = next((clause for clause in must_clauses if "term" in clause and "comment.keyword" in clause["term"]), None)
        assert comment_clause is not None
        assert comment_clause["term"]["comment.keyword"] == sample_comment_body.comment

    @pytest.mark.asyncio
    async def test_save_comment_skips_duplicate(self, mock_repo, sample_comment_body):
        """Test that save_comment skips saving when duplicate is detected."""
        # Mock search result indicating duplicate exists
        mock_search_result = MagicMock()
        mock_search_result.hits.total = 1
        mock_repo.search.return_value = mock_search_result

        # Create real instance with mocked methods
        repo = UserCommentsRepository.__new__(UserCommentsRepository)
        repo.search = mock_repo.search
        repo.save_new = mock_repo.save_new
        repo.index_for_record_model = mock_repo.index_for_record_model
        repo.tenancy = mock_repo.tenancy
        repo._is_duplicate_comment = AsyncMock(return_value=True)

        # Test save_comment method
        result = await repo.save_comment(sample_comment_body)
        
        assert result is None  # Should return None for duplicates
        mock_repo.save_new.assert_not_called()  # Should not save duplicate

    @pytest.mark.asyncio
    async def test_save_comment_allows_non_duplicate(self, mock_repo, sample_comment_body):
        """Test that save_comment saves when no duplicate is detected."""
        # Mock search result indicating no duplicate exists
        mock_search_result = MagicMock()
        mock_search_result.hits.total = 0
        mock_repo.search.return_value = mock_search_result
        mock_repo.save_new.return_value = MagicMock()

        # Create real instance with mocked methods
        repo = UserCommentsRepository.__new__(UserCommentsRepository)
        repo.search = mock_repo.search
        repo.save_new = mock_repo.save_new
        repo.index_for_record_model = mock_repo.index_for_record_model
        repo.tenancy = mock_repo.tenancy
        repo._is_duplicate_comment = AsyncMock(return_value=False)

        # Test save_comment method
        result = await repo.save_comment(sample_comment_body)
        
        assert result is not None  # Should return saved comment
        mock_repo.save_new.assert_called_once()  # Should save non-duplicate

    def test_time_window_calculation(self):
        """Test that the time window for duplicate detection is calculated correctly."""
        # This test verifies the time calculation logic
        current_time = dt.datetime(2023, 1, 1, 12, 0, 0)
        time_window_minutes = 5
        expected_threshold = current_time - dt.timedelta(minutes=time_window_minutes)
        
        assert expected_threshold == dt.datetime(2023, 1, 1, 11, 55, 0)
