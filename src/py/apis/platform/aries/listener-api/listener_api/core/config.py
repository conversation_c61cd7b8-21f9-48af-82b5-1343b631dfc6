"""API service config class."""

from pydantic import BaseSettings


class ApiServiceConfig(BaseSettings):
    """API service configuration."""

    API_PREFIX: str = "/api/v1.0/steeleye-listener-svc"
    HEALTH_CHECK: str = "/api/v1.0/steeleye-listener-svc/health"
    LOGGER: str = "uvicorn.access"


class EnvironmentVariables(BaseSettings):
    """Environment variables configuration."""

    DEBUG: bool = False
    VAULT_URL: str
    VAULT_MOUNT_POINT: str
    VAULT_TOKEN: str | None = None
    VAULT_AUTH_METHOD: str
    VAULT_K8S_JWT_PATH: str | None = None
    VAULT_K8S_ROLE: str | None = None
    VAULT_K8S_AUTH_MOUNT_POINT: str | None = None

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


service_config = ApiServiceConfig()
env_var = EnvironmentVariables()
